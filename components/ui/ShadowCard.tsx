import React, { ReactNode } from 'react';
import { Platform, StyleSheet, View, ViewStyle } from 'react-native';
import Colors from '../../constants/Colors';

interface ShadowCardProps {
  children: ReactNode;
  style?: ViewStyle;
  depth?: 'low' | 'medium' | 'high';
  borderRadius?: number;
}

export const ShadowCard: React.FC<ShadowCardProps> = ({
  children,
  style,
  depth = 'medium',
  borderRadius = 12,
}) => {
  const getShadowStyle = () => {
    switch (depth) {
      case 'low':
        return styles.shadowLow;
      case 'high':
        return styles.shadowHigh;
      case 'medium':
      default:
        return styles.shadowMedium;
    }
  };

  return (
    <View
      style={[
        styles.container,
        getShadowStyle(),
        { borderRadius },
        style,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    overflow: 'hidden',
  },
  shadowLow: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  shadowMedium: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 6,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  shadowHigh: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.2,
        shadowRadius: 10,
      },
      android: {
        elevation: 8,
      },
    }),
  },
});

export default ShadowCard; 