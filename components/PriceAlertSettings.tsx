import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Switch,
  Alert,
  ScrollView,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/Colors';
import ShadowCard from '@/components/ui/ShadowCard';
import { WatchedProduct } from '@/types/product';

interface PriceAlertSettingsProps {
  visible: boolean;
  onClose: () => void;
  onSave: (settings: PriceAlertSettings) => void;
  currentSettings?: WatchedProduct;
  productName: string;
  currentPrice: number;
}

export interface PriceAlertSettings {
  targetPrice?: number;
  notifyOnAnyDrop: boolean;
  dropThreshold: number;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

export default function PriceAlertSettings({
  visible,
  onClose,
  onSave,
  currentSettings,
  productName,
  currentPrice,
}: PriceAlertSettingsProps) {
  const [targetPrice, setTargetPrice] = useState(
    currentSettings?.targetPrice?.toString() || ''
  );
  const [notifyOnAnyDrop, setNotifyOnAnyDrop] = useState(
    currentSettings?.notifyOnAnyDrop ?? true
  );
  const [dropThreshold, setDropThreshold] = useState(
    currentSettings?.dropThreshold?.toString() || '5'
  );
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);

  const handleSave = useCallback(async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    const targetPriceNum = targetPrice ? parseFloat(targetPrice) : undefined;
    const dropThresholdNum = parseFloat(dropThreshold) || 5;
    
    // Validation
    if (targetPriceNum && targetPriceNum >= currentPrice) {
      Alert.alert(
        'Invalid Target Price',
        'Target price must be lower than the current price.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    if (dropThresholdNum < 1 || dropThresholdNum > 50) {
      Alert.alert(
        'Invalid Threshold',
        'Drop threshold must be between 1% and 50%.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    const settings: PriceAlertSettings = {
      targetPrice: targetPriceNum,
      notifyOnAnyDrop,
      dropThreshold: dropThresholdNum,
      emailNotifications,
      pushNotifications,
    };
    
    onSave(settings);
    onClose();
  }, [
    targetPrice,
    notifyOnAnyDrop,
    dropThreshold,
    emailNotifications,
    pushNotifications,
    currentPrice,
    onSave,
    onClose,
  ]);

  const handleReset = useCallback(() => {
    setTargetPrice('');
    setNotifyOnAnyDrop(true);
    setDropThreshold('5');
    setEmailNotifications(true);
    setPushNotifications(true);
  }, []);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ShadowCard style={styles.modal}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerLeft}>
                <FontAwesome name="bell" size={20} color={Colors.primary.start} />
                <Text style={styles.headerTitle}>Price Alert Settings</Text>
              </View>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <FontAwesome name="times" size={20} color={Colors.subtleText} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Product Info */}
              <View style={styles.productInfo}>
                <Text style={styles.productName}>{productName}</Text>
                <Text style={styles.currentPrice}>Current Price: ₹{currentPrice}</Text>
              </View>

              {/* Target Price */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Target Price (Optional)</Text>
                <Text style={styles.sectionDescription}>
                  Get notified when price drops to or below this amount
                </Text>
                <View style={styles.inputContainer}>
                  <Text style={styles.currencySymbol}>₹</Text>
                  <TextInput
                    style={styles.textInput}
                    value={targetPrice}
                    onChangeText={setTargetPrice}
                    placeholder="Enter target price"
                    keyboardType="numeric"
                    placeholderTextColor={Colors.subtleText}
                  />
                </View>
              </View>

              {/* Notify on Any Drop */}
              <View style={styles.section}>
                <View style={styles.switchRow}>
                  <View style={styles.switchInfo}>
                    <Text style={styles.switchTitle}>Notify on Any Drop</Text>
                    <Text style={styles.switchDescription}>
                      Get alerts for any price decrease
                    </Text>
                  </View>
                  <Switch
                    value={notifyOnAnyDrop}
                    onValueChange={setNotifyOnAnyDrop}
                    trackColor={{ false: Colors.border, true: Colors.primary.start }}
                    thumbColor={notifyOnAnyDrop ? '#FFFFFF' : Colors.subtleText}
                  />
                </View>
              </View>

              {/* Drop Threshold */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Minimum Drop Threshold</Text>
                <Text style={styles.sectionDescription}>
                  Only notify if price drops by at least this percentage
                </Text>
                <View style={styles.inputContainer}>
                  <TextInput
                    style={styles.textInput}
                    value={dropThreshold}
                    onChangeText={setDropThreshold}
                    placeholder="5"
                    keyboardType="numeric"
                    placeholderTextColor={Colors.subtleText}
                  />
                  <Text style={styles.percentSymbol}>%</Text>
                </View>
              </View>

              {/* Notification Preferences */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Notification Preferences</Text>
                
                <View style={styles.switchRow}>
                  <View style={styles.switchInfo}>
                    <Text style={styles.switchTitle}>Push Notifications</Text>
                    <Text style={styles.switchDescription}>
                      Instant alerts on your device
                    </Text>
                  </View>
                  <Switch
                    value={pushNotifications}
                    onValueChange={setPushNotifications}
                    trackColor={{ false: Colors.border, true: Colors.primary.start }}
                    thumbColor={pushNotifications ? '#FFFFFF' : Colors.subtleText}
                  />
                </View>

                <View style={styles.switchRow}>
                  <View style={styles.switchInfo}>
                    <Text style={styles.switchTitle}>Email Notifications</Text>
                    <Text style={styles.switchDescription}>
                      Detailed alerts via email
                    </Text>
                  </View>
                  <Switch
                    value={emailNotifications}
                    onValueChange={setEmailNotifications}
                    trackColor={{ false: Colors.border, true: Colors.primary.start }}
                    thumbColor={emailNotifications ? '#FFFFFF' : Colors.subtleText}
                  />
                </View>
              </View>

              {/* Preview */}
              <View style={styles.previewSection}>
                <Text style={styles.previewTitle}>Alert Preview</Text>
                <View style={styles.previewCard}>
                  <FontAwesome name="bell" size={16} color={Colors.primary.start} />
                  <View style={styles.previewContent}>
                    <Text style={styles.previewText}>
                      You'll be notified when {productName} price{' '}
                      {targetPrice ? `drops to ₹${targetPrice} or ` : ''}
                      drops by {dropThreshold}% or more
                    </Text>
                  </View>
                </View>
              </View>
            </ScrollView>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity style={styles.resetButton} onPress={handleReset}>
                <Text style={styles.resetButtonText}>Reset</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                <LinearGradient
                  colors={[Colors.primary.start, Colors.primary.end]}
                  style={styles.saveButtonGradient}
                >
                  <Text style={styles.saveButtonText}>Save Settings</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </ShadowCard>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
  },
  modal: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 12,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    maxHeight: 400,
  },
  productInfo: {
    padding: 20,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  currentPrice: {
    fontSize: 14,
    color: Colors.subtleText,
  },
  section: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.subtleText,
    marginBottom: 16,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: Colors.background,
  },
  currencySymbol: {
    fontSize: 16,
    color: Colors.text,
    marginRight: 8,
  },
  percentSymbol: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 8,
  },
  textInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.text,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  switchDescription: {
    fontSize: 14,
    color: Colors.subtleText,
    lineHeight: 18,
  },
  previewSection: {
    padding: 20,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  previewCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary.start,
  },
  previewContent: {
    flex: 1,
    marginLeft: 12,
  },
  previewText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: 'center',
  },
  resetButtonText: {
    color: Colors.subtleText,
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    flex: 2,
    borderRadius: 25,
  },
  saveButtonGradient: {
    paddingVertical: 14,
    alignItems: 'center',
    borderRadius: 25,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
