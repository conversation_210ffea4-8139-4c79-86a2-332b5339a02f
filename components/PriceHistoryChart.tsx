import Colors from '@/constants/Colors';
import { PricePoint } from '@/types/product';
import React from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import Svg, { Circle, Line, Path, Text as SvgText } from 'react-native-svg';

interface PriceHistoryChartProps {
  data: PricePoint[];
  width?: number;
  height?: number;
}

export default function PriceHistoryChart({ 
  data, 
  width = Dimensions.get('window').width - 40, 
  height = 180 
}: PriceHistoryChartProps) {
  if (!data || data.length < 2) {
    return (
      <View style={[styles.container, { width, height }]}>
        <Text style={styles.noDataText}>
          Not enough price history data to display chart
        </Text>
      </View>
    );
  }

  // Sort data by date
  const sortedData = [...data].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  // Calculate chart dimensions
  const padding = 40;
  const chartWidth = width - (padding * 2);
  const chartHeight = height - (padding * 2);

  // Find min and max prices for scaling
  const prices = sortedData.map(point => point.price);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);
  const priceRange = maxPrice - minPrice;

  // Add some padding to the price range
  const paddedMin = minPrice - (priceRange * 0.1);
  const paddedMax = maxPrice + (priceRange * 0.1);
  const paddedRange = paddedMax - paddedMin;

  // Generate path for the line chart
  const generatePath = () => {
    let path = '';
    
    sortedData.forEach((point, index) => {
      const x = padding + (index / (sortedData.length - 1)) * chartWidth;
      const y = padding + chartHeight - ((point.price - paddedMin) / paddedRange) * chartHeight;
      
      if (index === 0) {
        path += `M ${x} ${y}`;
      } else {
        path += ` L ${x} ${y}`;
      }
    });
    
    return path;
  };

  // Generate points for dots
  const generatePoints = () => {
    return sortedData.map((point, index) => {
      const x = padding + (index / (sortedData.length - 1)) * chartWidth;
      const y = padding + chartHeight - ((point.price - paddedMin) / paddedRange) * chartHeight;
      
      return { x, y, price: point.price, date: point.date };
    });
  };

  const points = generatePoints();
  const pathData = generatePath();

  // Format date for labels
  const formatDate = (date: Date) => {
    const d = new Date(date);
    return `${d.getDate()}/${d.getMonth() + 1}`;
  };

  // Format price for labels
  const formatPrice = (price: number) => {
    return `₹${Math.round(price)}`;
  };

  return (
    <View style={[styles.container, { width, height }]}>
      <Svg width={width} height={height}>
        {/* Grid lines */}
        {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
          const y = padding + ratio * chartHeight;
          const price = paddedMax - (ratio * paddedRange);
          
          return (
            <React.Fragment key={`grid-${index}`}>
              <Line
                x1={padding}
                y1={y}
                x2={padding + chartWidth}
                y2={y}
                stroke="#E0E0E0"
                strokeWidth="1"
                strokeDasharray="3,3"
              />
              <SvgText
                x={padding - 5}
                y={y + 4}
                fontSize="10"
                fill={Colors.subtleText}
                textAnchor="end"
              >
                {formatPrice(price)}
              </SvgText>
            </React.Fragment>
          );
        })}

        {/* Chart line */}
        <Path
          d={pathData}
          stroke={Colors.primary.start}
          strokeWidth="2"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* Data points */}
        {points.map((point, index) => (
          <Circle
            key={`point-${index}`}
            cx={point.x}
            cy={point.y}
            r="4"
            fill={Colors.primary.start}
            stroke="#FFFFFF"
            strokeWidth="2"
          />
        ))}

        {/* Date labels */}
        {points.map((point, index) => {
          // Only show labels for first, last, and middle points to avoid crowding
          const shouldShowLabel = index === 0 || 
                                 index === points.length - 1 || 
                                 (points.length > 4 && index === Math.floor(points.length / 2));
          
          if (!shouldShowLabel) return null;
          
          return (
            <SvgText
              key={`date-${index}`}
              x={point.x}
              y={height - 10}
              fontSize="10"
              fill={Colors.subtleText}
              textAnchor="middle"
            >
              {formatDate(point.date)}
            </SvgText>
          );
        })}
      </Svg>

      {/* Chart info */}
      <View style={styles.chartInfo}>
        <View style={styles.priceRange}>
          <View style={styles.priceItem}>
            <Text style={styles.priceLabel}>Lowest</Text>
            <Text style={[styles.priceValue, { color: Colors.accent.success }]}>
              {formatPrice(minPrice)}
            </Text>
          </View>
          <View style={styles.priceItem}>
            <Text style={styles.priceLabel}>Highest</Text>
            <Text style={[styles.priceValue, { color: Colors.accent.error }]}>
              {formatPrice(maxPrice)}
            </Text>
          </View>
          <View style={styles.priceItem}>
            <Text style={styles.priceLabel}>Current</Text>
            <Text style={styles.priceValue}>
              {formatPrice(sortedData[sortedData.length - 1].price)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  noDataText: {
    textAlign: 'center',
    color: Colors.subtleText,
    fontSize: 14,
    marginTop: 60,
  },
  chartInfo: {
    marginTop: 16,
  },
  priceRange: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  priceItem: {
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    color: Colors.subtleText,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
});
