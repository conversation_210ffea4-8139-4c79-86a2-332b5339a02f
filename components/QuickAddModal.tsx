import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/Colors';
import ShadowCard from '@/components/ui/ShadowCard';
import { Product } from '@/types/product';

interface QuickAddModalProps {
  visible: boolean;
  product: Product | null;
  onClose: () => void;
  onAddToCart: (product: Product, quantity: number) => void;
  onViewDetails: (product: Product) => void;
  onAddToWatchlist: (product: Product) => void;
}

export default function QuickAddModal({
  visible,
  product,
  onClose,
  onAddToCart,
  onViewDetails,
  onAddToWatchlist,
}: QuickAddModalProps) {
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(false);

  const handleQuantityChange = (delta: number) => {
    const newQuantity = Math.max(1, quantity + delta);
    setQuantity(newQuantity);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleAddToCart = async () => {
    if (!product) return;
    
    setLoading(true);
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      await onAddToCart(product, quantity);
      Alert.alert(
        'Added to Cart',
        `${quantity} x ${product.name} added to your cart`,
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert('Error', 'Failed to add item to cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetails = () => {
    if (!product) return;
    onViewDetails(product);
    onClose();
  };

  const handleAddToWatchlist = async () => {
    if (!product) return;
    
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    try {
      await onAddToWatchlist(product);
      Alert.alert(
        'Added to Watchlist',
        `You'll be notified when ${product.name} price drops`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error adding to watchlist:', error);
      Alert.alert('Error', 'Failed to add to watchlist. Please try again.');
    }
  };

  if (!product) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <ShadowCard style={styles.modal}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Product Found!</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <FontAwesome name="times" size={20} color={Colors.subtleText} />
              </TouchableOpacity>
            </View>

            {/* Product Info */}
            <View style={styles.productInfo}>
              <Text style={styles.productEmoji}>{product.imageUrl}</Text>
              <View style={styles.productDetails}>
                <Text style={styles.productName}>{product.name}</Text>
                <Text style={styles.productBrand}>{product.brand}</Text>
                <Text style={styles.productWeight}>{product.weight}</Text>
              </View>
            </View>

            {/* Price Section */}
            <View style={styles.priceSection}>
              <View style={styles.priceRow}>
                <Text style={styles.currentPrice}>₹{product.price}</Text>
                {product.originalPrice && product.originalPrice > product.price && (
                  <>
                    <Text style={styles.originalPrice}>₹{product.originalPrice}</Text>
                    <View style={styles.discountBadge}>
                      <Text style={styles.discountText}>{product.discount}% OFF</Text>
                    </View>
                  </>
                )}
              </View>
              {product.originalPrice && product.originalPrice > product.price && (
                <Text style={styles.savings}>
                  You save ₹{product.originalPrice - product.price}
                </Text>
              )}
            </View>

            {/* Stock Status */}
            <View style={styles.stockSection}>
              <FontAwesome 
                name={product.inStock ? "check-circle" : "times-circle"} 
                size={16} 
                color={product.inStock ? Colors.accent.success : Colors.accent.error} 
              />
              <Text style={[
                styles.stockText, 
                { color: product.inStock ? Colors.accent.success : Colors.accent.error }
              ]}>
                {product.inStock ? 'In Stock' : 'Out of Stock'}
              </Text>
            </View>

            {/* Quantity Selector */}
            {product.inStock && (
              <View style={styles.quantitySection}>
                <Text style={styles.quantityLabel}>Quantity</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity 
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(-1)}
                    disabled={quantity <= 1}
                  >
                    <FontAwesome 
                      name="minus" 
                      size={16} 
                      color={quantity <= 1 ? Colors.subtleText : Colors.primary.start} 
                    />
                  </TouchableOpacity>
                  <Text style={styles.quantityText}>{quantity}</Text>
                  <TouchableOpacity 
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(1)}
                  >
                    <FontAwesome name="plus" size={16} color={Colors.primary.start} />
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              {product.inStock ? (
                <TouchableOpacity 
                  style={styles.addToCartButton}
                  onPress={handleAddToCart}
                  disabled={loading}
                >
                  <LinearGradient
                    colors={[Colors.primary.start, Colors.primary.end]}
                    style={styles.addToCartGradient}
                  >
                    {loading ? (
                      <ActivityIndicator size="small" color="#FFFFFF" />
                    ) : (
                      <>
                        <FontAwesome name="shopping-cart" size={18} color="#FFFFFF" />
                        <Text style={styles.addToCartText}>Add to Cart</Text>
                      </>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity 
                  style={styles.watchlistButton}
                  onPress={handleAddToWatchlist}
                >
                  <FontAwesome name="bell" size={18} color={Colors.primary.start} />
                  <Text style={styles.watchlistButtonText}>Add to Watchlist</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={styles.detailsButton}
                onPress={handleViewDetails}
              >
                <Text style={styles.detailsButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </ShadowCard>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '100%',
    maxWidth: 400,
  },
  modal: {
    padding: 24,
    borderRadius: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  productEmoji: {
    fontSize: 40,
    marginRight: 16,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 14,
    color: Colors.subtleText,
    marginBottom: 2,
  },
  productWeight: {
    fontSize: 12,
    color: Colors.subtleText,
  },
  priceSection: {
    marginBottom: 12,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  currentPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary.start,
    marginRight: 12,
  },
  originalPrice: {
    fontSize: 16,
    color: Colors.subtleText,
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  discountBadge: {
    backgroundColor: Colors.accent.success,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  savings: {
    fontSize: 12,
    color: Colors.accent.success,
    fontWeight: '500',
  },
  stockSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stockText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  quantitySection: {
    marginBottom: 20,
  },
  quantityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginHorizontal: 20,
    minWidth: 30,
    textAlign: 'center',
  },
  actionButtons: {
    gap: 12,
  },
  addToCartButton: {
    borderRadius: 25,
  },
  addToCartGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 25,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  watchlistButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 25,
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.primary.start,
  },
  watchlistButtonText: {
    color: Colors.primary.start,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  detailsButton: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  detailsButtonText: {
    color: Colors.primary.start,
    fontSize: 16,
    fontWeight: '500',
  },
});
