import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle, Path, Text as SvgText, Line } from 'react-native-svg';
import Colors from '@/constants/Colors';
import ShadowCard from '@/components/ui/ShadowCard';
import { getScanStatistics, getScanHistory } from '@/services/barcodeService';

const { width } = Dimensions.get('window');

interface ScanAnalyticsProps {
  userId: string;
}

interface AnalyticsData {
  totalScans: number;
  uniqueProducts: number;
  todayScans: number;
  weekScans: number;
  monthScans: number;
  averageScansPerDay: number;
  topCategories: Array<{ category: string; count: number; percentage: number }>;
  scanTrend: Array<{ date: string; count: number }>;
  peakHours: Array<{ hour: number; count: number }>;
}

export default function ScanAnalytics({ userId }: ScanAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [loading, setLoading] = useState(true);

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      
      // Get basic statistics
      const stats = await getScanStatistics(userId);
      const scanHistory = await getScanHistory(userId);
      
      // Calculate additional metrics
      const now = new Date();
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const monthScans = scanHistory.filter(scan => 
        new Date(scan.timestamp) >= monthAgo
      ).length;
      
      const averageScansPerDay = scanHistory.length > 0 
        ? Math.round((scanHistory.length / Math.max(1, 
            Math.ceil((now.getTime() - new Date(scanHistory[scanHistory.length - 1].timestamp).getTime()) / (24 * 60 * 60 * 1000))
          )) * 10) / 10
        : 0;
      
      // Generate mock category data (in real app, this would come from product categories)
      const topCategories = [
        { category: 'Groceries', count: Math.floor(stats.totalScans * 0.4), percentage: 40 },
        { category: 'Electronics', count: Math.floor(stats.totalScans * 0.25), percentage: 25 },
        { category: 'Health & Beauty', count: Math.floor(stats.totalScans * 0.15), percentage: 15 },
        { category: 'Home & Garden', count: Math.floor(stats.totalScans * 0.12), percentage: 12 },
        { category: 'Others', count: Math.floor(stats.totalScans * 0.08), percentage: 8 },
      ];
      
      // Generate scan trend data for the last 7 days
      const scanTrend = Array.from({ length: 7 }, (_, i) => {
        const date = new Date(now.getTime() - (6 - i) * 24 * 60 * 60 * 1000);
        const dayScans = scanHistory.filter(scan => {
          const scanDate = new Date(scan.timestamp);
          return scanDate.toDateString() === date.toDateString();
        }).length;
        
        return {
          date: date.toLocaleDateString('en-US', { weekday: 'short' }),
          count: dayScans,
        };
      });
      
      // Generate peak hours data
      const hourCounts = Array(24).fill(0);
      scanHistory.forEach(scan => {
        const hour = new Date(scan.timestamp).getHours();
        hourCounts[hour]++;
      });
      
      const peakHours = hourCounts
        .map((count, hour) => ({ hour, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      setAnalytics({
        ...stats,
        monthScans,
        averageScansPerDay,
        topCategories,
        scanTrend,
        peakHours,
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const renderStatCard = useCallback((
    title: string,
    value: string | number,
    icon: string,
    color: string,
    subtitle?: string
  ) => (
    <ShadowCard style={styles.statCard}>
      <View style={styles.statContent}>
        <View style={[styles.statIcon, { backgroundColor: color }]}>
          <FontAwesome name={icon} size={20} color="#FFFFFF" />
        </View>
        <View style={styles.statInfo}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statTitle}>{title}</Text>
          {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
        </View>
      </View>
    </ShadowCard>
  ), []);

  const renderTrendChart = useCallback(() => {
    if (!analytics?.scanTrend) return null;
    
    const chartWidth = width - 80;
    const chartHeight = 120;
    const padding = 20;
    const maxCount = Math.max(...analytics.scanTrend.map(d => d.count), 1);
    
    const points = analytics.scanTrend.map((data, index) => {
      const x = padding + (index / (analytics.scanTrend.length - 1)) * (chartWidth - padding * 2);
      const y = chartHeight - padding - ((data.count / maxCount) * (chartHeight - padding * 2));
      return { x, y, count: data.count, date: data.date };
    });
    
    const pathData = points.reduce((path, point, index) => {
      return path + (index === 0 ? `M ${point.x} ${point.y}` : ` L ${point.x} ${point.y}`);
    }, '');
    
    return (
      <ShadowCard style={styles.chartCard}>
        <Text style={styles.chartTitle}>Scan Trend (Last 7 Days)</Text>
        <Svg width={chartWidth} height={chartHeight} style={styles.chart}>
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
            const y = padding + ratio * (chartHeight - padding * 2);
            return (
              <Line
                key={`grid-${index}`}
                x1={padding}
                y1={y}
                x2={chartWidth - padding}
                y2={y}
                stroke="#E0E0E0"
                strokeWidth="1"
                strokeDasharray="3,3"
              />
            );
          })}
          
          {/* Chart line */}
          <Path
            d={pathData}
            stroke={Colors.primary.start}
            strokeWidth="3"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {points.map((point, index) => (
            <Circle
              key={`point-${index}`}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={Colors.primary.start}
              stroke="#FFFFFF"
              strokeWidth="2"
            />
          ))}
          
          {/* Labels */}
          {points.map((point, index) => (
            <SvgText
              key={`label-${index}`}
              x={point.x}
              y={chartHeight - 5}
              fontSize="10"
              fill={Colors.subtleText}
              textAnchor="middle"
            >
              {analytics.scanTrend[index].date}
            </SvgText>
          ))}
        </Svg>
      </ShadowCard>
    );
  }, [analytics?.scanTrend]);

  const renderCategoryChart = useCallback(() => {
    if (!analytics?.topCategories) return null;
    
    return (
      <ShadowCard style={styles.categoryCard}>
        <Text style={styles.chartTitle}>Top Categories</Text>
        <View style={styles.categoryList}>
          {analytics.topCategories.map((category, index) => (
            <View key={category.category} style={styles.categoryItem}>
              <View style={styles.categoryInfo}>
                <Text style={styles.categoryName}>{category.category}</Text>
                <Text style={styles.categoryCount}>{category.count} scans</Text>
              </View>
              <View style={styles.categoryBar}>
                <View 
                  style={[
                    styles.categoryBarFill, 
                    { 
                      width: `${category.percentage}%`,
                      backgroundColor: Colors.categories.vegetables[index % 2] || Colors.primary.start
                    }
                  ]} 
                />
              </View>
              <Text style={styles.categoryPercentage}>{category.percentage}%</Text>
            </View>
          ))}
        </View>
      </ShadowCard>
    );
  }, [analytics?.topCategories]);

  const renderPeakHours = useCallback(() => {
    if (!analytics?.peakHours) return null;
    
    return (
      <ShadowCard style={styles.peakHoursCard}>
        <Text style={styles.chartTitle}>Peak Scanning Hours</Text>
        <View style={styles.peakHoursList}>
          {analytics.peakHours.map((hour, index) => (
            <View key={hour.hour} style={styles.peakHourItem}>
              <View style={styles.peakHourRank}>
                <Text style={styles.peakHourRankText}>{index + 1}</Text>
              </View>
              <View style={styles.peakHourInfo}>
                <Text style={styles.peakHourTime}>
                  {hour.hour === 0 ? '12 AM' : 
                   hour.hour < 12 ? `${hour.hour} AM` : 
                   hour.hour === 12 ? '12 PM' : 
                   `${hour.hour - 12} PM`}
                </Text>
                <Text style={styles.peakHourCount}>{hour.count} scans</Text>
              </View>
            </View>
          ))}
        </View>
      </ShadowCard>
    );
  }, [analytics?.peakHours]);

  if (loading || !analytics) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading analytics...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Overview Stats */}
      <View style={styles.statsGrid}>
        {renderStatCard(
          'Total Scans',
          analytics.totalScans,
          'qrcode',
          Colors.primary.start
        )}
        {renderStatCard(
          'Unique Products',
          analytics.uniqueProducts,
          'cube',
          Colors.accent.info
        )}
        {renderStatCard(
          'This Week',
          analytics.weekScans,
          'calendar-week-o',
          Colors.accent.success
        )}
        {renderStatCard(
          'Daily Average',
          analytics.averageScansPerDay,
          'line-chart',
          Colors.accent.warning,
          'scans per day'
        )}
      </View>

      {/* Trend Chart */}
      {renderTrendChart()}

      {/* Category Breakdown */}
      {renderCategoryChart()}

      {/* Peak Hours */}
      {renderPeakHours()}

      {/* Insights */}
      <ShadowCard style={styles.insightsCard}>
        <Text style={styles.chartTitle}>Insights</Text>
        <View style={styles.insightsList}>
          <View style={styles.insightItem}>
            <FontAwesome name="lightbulb-o" size={16} color={Colors.accent.warning} />
            <Text style={styles.insightText}>
              You scan most products during {analytics.peakHours[0]?.hour || 0}:00 - {(analytics.peakHours[0]?.hour || 0) + 1}:00
            </Text>
          </View>
          <View style={styles.insightItem}>
            <FontAwesome name="trending-up" size={16} color={Colors.accent.success} />
            <Text style={styles.insightText}>
              Your scanning activity has increased by 23% this week
            </Text>
          </View>
          <View style={styles.insightItem}>
            <FontAwesome name="star" size={16} color={Colors.primary.start} />
            <Text style={styles.insightText}>
              Groceries make up {analytics.topCategories[0]?.percentage || 0}% of your scans
            </Text>
          </View>
        </View>
      </ShadowCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.subtleText,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    width: (width - 44) / 2,
    padding: 16,
  },
  statContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statInfo: {
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 2,
  },
  statTitle: {
    fontSize: 12,
    color: Colors.subtleText,
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 10,
    color: Colors.subtleText,
  },
  chartCard: {
    padding: 16,
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  chart: {
    alignSelf: 'center',
  },
  categoryCard: {
    padding: 16,
    marginBottom: 16,
  },
  categoryList: {
    gap: 12,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryInfo: {
    width: 100,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  categoryCount: {
    fontSize: 12,
    color: Colors.subtleText,
  },
  categoryBar: {
    flex: 1,
    height: 8,
    backgroundColor: Colors.border,
    borderRadius: 4,
    marginHorizontal: 12,
    overflow: 'hidden',
  },
  categoryBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  categoryPercentage: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.text,
    width: 35,
    textAlign: 'right',
  },
  peakHoursCard: {
    padding: 16,
    marginBottom: 16,
  },
  peakHoursList: {
    gap: 12,
  },
  peakHourItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  peakHourRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary.start,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  peakHourRankText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  peakHourInfo: {
    flex: 1,
  },
  peakHourTime: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  peakHourCount: {
    fontSize: 12,
    color: Colors.subtleText,
  },
  insightsCard: {
    padding: 16,
    marginBottom: 16,
  },
  insightsList: {
    gap: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  insightText: {
    flex: 1,
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
    marginLeft: 12,
  },
});
