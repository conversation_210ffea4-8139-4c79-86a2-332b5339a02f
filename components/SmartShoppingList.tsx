import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/Colors';
import ShadowCard from '@/components/ui/ShadowCard';
import { Product } from '@/types/product';
import { generateRecommendations, ProductRecommendation } from '@/services/recommendationEngine';

interface ShoppingListItem {
  id: string;
  productId?: string;
  name: string;
  quantity: number;
  completed: boolean;
  price?: number;
  category?: string;
  addedAt: Date;
  completedAt?: Date;
  notes?: string;
}

interface SmartShoppingListProps {
  userId: string;
  onProductSelect?: (product: Product) => void;
  onScanRequest?: () => void;
}

export default function SmartShoppingList({
  userId,
  onProductSelect,
  onScanRequest,
}: SmartShoppingListProps) {
  const [items, setItems] = useState<ShoppingListItem[]>([]);
  const [newItemName, setNewItemName] = useState('');
  const [recommendations, setRecommendations] = useState<ProductRecommendation[]>([]);
  const [showRecommendations, setShowRecommendations] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editingItem, setEditingItem] = useState<ShoppingListItem | null>(null);

  useEffect(() => {
    loadShoppingList();
    loadRecommendations();
  }, [userId]);

  const loadShoppingList = useCallback(async () => {
    // In a real app, this would load from API/storage
    // For demo, using mock data
    const mockItems: ShoppingListItem[] = [
      {
        id: '1',
        productId: 'p1',
        name: 'Organic Bananas',
        quantity: 2,
        completed: false,
        price: 49,
        category: 'fruits',
        addedAt: new Date(),
      },
      {
        id: '2',
        name: 'Whole Wheat Bread',
        quantity: 1,
        completed: true,
        price: 35,
        category: 'bakery',
        addedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        completedAt: new Date(),
      },
    ];
    setItems(mockItems);
  }, []);

  const loadRecommendations = useCallback(async () => {
    try {
      setLoading(true);
      const recs = await generateRecommendations(userId, 5);
      setRecommendations(recs);
    } catch (error) {
      console.error('Error loading recommendations:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const addItem = useCallback(async () => {
    if (!newItemName.trim()) return;

    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const newItem: ShoppingListItem = {
      id: Date.now().toString(),
      name: newItemName.trim(),
      quantity: 1,
      completed: false,
      addedAt: new Date(),
    };

    setItems(prev => [newItem, ...prev]);
    setNewItemName('');
  }, [newItemName]);

  const toggleItem = useCallback(async (itemId: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    setItems(prev => prev.map(item => 
      item.id === itemId 
        ? { 
            ...item, 
            completed: !item.completed,
            completedAt: !item.completed ? new Date() : undefined,
          }
        : item
    ));
  }, []);

  const deleteItem = useCallback(async (itemId: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    Alert.alert(
      'Delete Item',
      'Are you sure you want to remove this item from your list?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setItems(prev => prev.filter(item => item.id !== itemId));
          },
        },
      ]
    );
  }, []);

  const updateQuantity = useCallback((itemId: string, delta: number) => {
    setItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, quantity: Math.max(1, item.quantity + delta) }
        : item
    ));
  }, []);

  const addRecommendedItem = useCallback(async (recommendation: ProductRecommendation) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    const newItem: ShoppingListItem = {
      id: Date.now().toString(),
      productId: recommendation.product.id,
      name: recommendation.product.name,
      quantity: 1,
      completed: false,
      price: recommendation.product.price,
      category: recommendation.product.category,
      addedAt: new Date(),
    };

    setItems(prev => [newItem, ...prev]);
    setShowRecommendations(false);
  }, []);

  const clearCompleted = useCallback(() => {
    Alert.alert(
      'Clear Completed',
      'Remove all completed items from your list?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          onPress: () => {
            setItems(prev => prev.filter(item => !item.completed));
          },
        },
      ]
    );
  }, []);

  const renderItem = useCallback(({ item }: { item: ShoppingListItem }) => (
    <ShadowCard style={[styles.itemCard, item.completed && styles.completedCard]}>
      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => toggleItem(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.itemLeft}>
          <TouchableOpacity
            style={[styles.checkbox, item.completed && styles.checkedBox]}
            onPress={() => toggleItem(item.id)}
          >
            {item.completed && (
              <FontAwesome name="check" size={14} color="#FFFFFF" />
            )}
          </TouchableOpacity>
          
          <View style={styles.itemInfo}>
            <Text style={[styles.itemName, item.completed && styles.completedText]}>
              {item.name}
            </Text>
            {item.category && (
              <Text style={styles.itemCategory}>{item.category}</Text>
            )}
            {item.price && (
              <Text style={styles.itemPrice}>₹{item.price}</Text>
            )}
          </View>
        </View>

        <View style={styles.itemRight}>
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => updateQuantity(item.id, -1)}
              disabled={item.quantity <= 1}
            >
              <FontAwesome 
                name="minus" 
                size={12} 
                color={item.quantity <= 1 ? Colors.subtleText : Colors.primary.start} 
              />
            </TouchableOpacity>
            
            <Text style={styles.quantityText}>{item.quantity}</Text>
            
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => updateQuantity(item.id, 1)}
            >
              <FontAwesome name="plus" size={12} color={Colors.primary.start} />
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => deleteItem(item.id)}
          >
            <FontAwesome name="trash" size={16} color={Colors.accent.error} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </ShadowCard>
  ), [toggleItem, updateQuantity, deleteItem]);

  const renderRecommendation = useCallback(({ item }: { item: ProductRecommendation }) => (
    <TouchableOpacity
      style={styles.recommendationCard}
      onPress={() => addRecommendedItem(item)}
      activeOpacity={0.7}
    >
      <View style={styles.recommendationContent}>
        <Text style={styles.recommendationEmoji}>{item.product.imageUrl}</Text>
        <View style={styles.recommendationInfo}>
          <Text style={styles.recommendationName}>{item.product.name}</Text>
          <Text style={styles.recommendationPrice}>₹{item.product.price}</Text>
          <Text style={styles.recommendationReason}>{item.reasons[0]}</Text>
        </View>
        <View style={styles.recommendationBadge}>
          <Text style={styles.recommendationBadgeText}>
            {Math.round(item.score * 100)}%
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  ), [addRecommendedItem]);

  const completedItems = items.filter(item => item.completed);
  const pendingItems = items.filter(item => !item.completed);
  const totalPrice = items.reduce((sum, item) => sum + ((item.price || 0) * item.quantity), 0);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Smart Shopping List</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowRecommendations(true)}
          >
            <FontAwesome name="lightbulb-o" size={20} color={Colors.primary.start} />
          </TouchableOpacity>
          {onScanRequest && (
            <TouchableOpacity
              style={styles.headerButton}
              onPress={onScanRequest}
            >
              <FontAwesome name="qrcode" size={20} color={Colors.primary.start} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Add Item */}
      <ShadowCard style={styles.addItemCard}>
        <View style={styles.addItemRow}>
          <TextInput
            style={styles.addItemInput}
            value={newItemName}
            onChangeText={setNewItemName}
            placeholder="Add item to your list..."
            placeholderTextColor={Colors.subtleText}
            onSubmitEditing={addItem}
            returnKeyType="done"
          />
          <TouchableOpacity
            style={styles.addButton}
            onPress={addItem}
            disabled={!newItemName.trim()}
          >
            <LinearGradient
              colors={[Colors.primary.start, Colors.primary.end]}
              style={styles.addButtonGradient}
            >
              <FontAwesome name="plus" size={16} color="#FFFFFF" />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </ShadowCard>

      {/* Summary */}
      {items.length > 0 && (
        <ShadowCard style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryText}>
              {pendingItems.length} pending, {completedItems.length} completed
            </Text>
            <Text style={styles.totalPrice}>Total: ₹{totalPrice}</Text>
          </View>
          {completedItems.length > 0 && (
            <TouchableOpacity style={styles.clearButton} onPress={clearCompleted}>
              <Text style={styles.clearButtonText}>Clear Completed</Text>
            </TouchableOpacity>
          )}
        </ShadowCard>
      )}

      {/* Items List */}
      <FlatList
        data={[...pendingItems, ...completedItems]}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <FontAwesome name="list" size={64} color={Colors.subtleText} />
            <Text style={styles.emptyTitle}>Your list is empty</Text>
            <Text style={styles.emptyText}>Add items to get started</Text>
          </View>
        }
      />

      {/* Recommendations Modal */}
      <Modal
        visible={showRecommendations}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowRecommendations(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <ShadowCard style={styles.modal}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Recommended for You</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowRecommendations(false)}
                >
                  <FontAwesome name="times" size={20} color={Colors.subtleText} />
                </TouchableOpacity>
              </View>

              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={Colors.primary.start} />
                  <Text style={styles.loadingText}>Loading recommendations...</Text>
                </View>
              ) : (
                <FlatList
                  data={recommendations}
                  keyExtractor={(item) => item.product.id}
                  renderItem={renderRecommendation}
                  contentContainerStyle={styles.recommendationsList}
                  showsVerticalScrollIndicator={false}
                  ListEmptyComponent={
                    <View style={styles.emptyRecommendations}>
                      <Text style={styles.emptyRecommendationsText}>
                        No recommendations available
                      </Text>
                    </View>
                  }
                />
              )}
            </ShadowCard>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  addItemCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 16,
  },
  addItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  addItemInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: Colors.background,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  addButton: {
    borderRadius: 25,
  },
  addButtonGradient: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    padding: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.subtleText,
  },
  totalPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.start,
  },
  clearButton: {
    alignSelf: 'flex-start',
  },
  clearButtonText: {
    fontSize: 14,
    color: Colors.accent.error,
    fontWeight: '500',
  },
  listContainer: {
    padding: 20,
    paddingTop: 0,
  },
  itemCard: {
    marginBottom: 12,
    padding: 16,
  },
  completedCard: {
    opacity: 0.6,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  checkedBox: {
    backgroundColor: Colors.accent.success,
    borderColor: Colors.accent.success,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 2,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: Colors.subtleText,
  },
  itemCategory: {
    fontSize: 12,
    color: Colors.subtleText,
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: 14,
    color: Colors.primary.start,
    fontWeight: '500',
  },
  itemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
  quantityText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    minWidth: 20,
    textAlign: 'center',
  },
  deleteButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 20,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.subtleText,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    height: '70%',
  },
  modal: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.subtleText,
  },
  recommendationsList: {
    padding: 20,
  },
  recommendationCard: {
    marginBottom: 12,
    padding: 16,
    backgroundColor: Colors.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  recommendationContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recommendationEmoji: {
    fontSize: 32,
    marginRight: 12,
  },
  recommendationInfo: {
    flex: 1,
  },
  recommendationName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  recommendationPrice: {
    fontSize: 14,
    color: Colors.primary.start,
    fontWeight: '500',
    marginBottom: 2,
  },
  recommendationReason: {
    fontSize: 12,
    color: Colors.subtleText,
  },
  recommendationBadge: {
    backgroundColor: Colors.primary.start,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  recommendationBadgeText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  emptyRecommendations: {
    padding: 40,
    alignItems: 'center',
  },
  emptyRecommendationsText: {
    fontSize: 16,
    color: Colors.subtleText,
    textAlign: 'center',
  },
});
