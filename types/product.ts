// Product-related types for the grocery delivery app

export interface PricePoint {
  price: number;
  date: Date;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  category: string;
  subcategory?: string;
  brand?: string;
  weight?: string;
  unit?: string;
  imageUrl?: string;
  inStock: boolean;
  stockQuantity?: number;
  barcode?: string; // For barcode scanner feature
  
  // Price tracking features
  priceHistory: PricePoint[];
  lowestPrice?: number;
  highestPrice?: number;
  
  // Product attributes for filtering
  dietaryInfo?: DietaryInfo;
  tags?: string[];
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

export interface DietaryInfo {
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  isDairyFree: boolean;
  isNutFree: boolean;
  isKeto: boolean;
  isOrganic: boolean;
  containsAllergens: string[]; // e.g., ["peanuts", "shellfish", "soy"]
  dietaryTags: string[]; // e.g., ["low-carb", "high-protein", "sugar-free"]
}

export interface WatchedProduct {
  productId: string;
  targetPrice?: number; // Optional price target for alerts
  notifyOnAnyDrop: boolean; // Notify on any price drop
  dropThreshold?: number; // Minimum percentage drop to trigger alert (e.g., 5 for 5%)
  addedAt: Date;
}

export interface ScanHistory {
  id: string;
  userId: string;
  barcode: string;
  productId: string;
  timestamp: Date;
}

export interface PriceAlert {
  id: string;
  userId: string;
  productId: string;
  oldPrice: number;
  newPrice: number;
  dropPercent: number;
  notificationSent: boolean;
  createdAt: Date;
}

// Category types
export interface Category {
  id: string;
  name: string;
  icon: string;
  color: string[];
  description?: string;
  parentId?: string; // For subcategories
  sortOrder: number;
}

// Search and filter types
export interface ProductFilter {
  category?: string;
  subcategory?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  dietary?: {
    vegetarian?: boolean;
    vegan?: boolean;
    glutenFree?: boolean;
    dairyFree?: boolean;
    nutFree?: boolean;
    keto?: boolean;
    organic?: boolean;
  };
  inStock?: boolean;
  onSale?: boolean;
  brands?: string[];
  tags?: string[];
}

export interface SearchResult {
  products: Product[];
  totalCount: number;
  filters: {
    categories: { id: string; name: string; count: number }[];
    brands: { name: string; count: number }[];
    priceRange: { min: number; max: number };
  };
}

// Cart and order types
export interface CartItem {
  productId: string;
  product: Product;
  quantity: number;
  addedAt: Date;
  specialInstructions?: string;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  subtotal: number;
  discount: number;
  deliveryFee: number;
  total: number;
  updatedAt: Date;
}

// Recommendation types
export interface ProductRecommendation {
  product: Product;
  reason: 'frequently_bought' | 'price_drop' | 'trending' | 'similar_users' | 'seasonal';
  confidence: number; // 0-1 score
  metadata?: {
    previousPurchases?: number;
    priceDropPercent?: number;
    similarUserCount?: number;
  };
}

// Analytics types for smart features
export interface PurchasePattern {
  productId: string;
  frequency: number; // purchases per month
  averageQuantity: number;
  preferredDayOfWeek?: number; // 0-6
  seasonality?: 'spring' | 'summer' | 'fall' | 'winter';
  lastPurchased: Date;
}

export interface UserPreferences {
  favoriteCategories: string[];
  dietaryRestrictions: DietaryInfo;
  budgetRange: {
    weekly?: number;
    monthly?: number;
  };
  deliveryPreferences: {
    preferredTimeSlots: string[];
    addressId: string;
  };
  notificationSettings: {
    priceAlerts: boolean;
    stockAlerts: boolean;
    promotions: boolean;
    orderUpdates: boolean;
  };
}
