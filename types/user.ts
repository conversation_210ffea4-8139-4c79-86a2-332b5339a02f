// User-related types for the grocery delivery app

import { PurchasePattern, UserPreferences, WatchedProduct } from './product';

export interface User {
  id: string;
  email: string;
  phone?: string;
  name: string;
  avatar?: string;
  
  // Address information
  addresses: Address[];
  defaultAddressId?: string;
  
  // Price tracking features
  watchedProducts: WatchedProduct[];
  
  // User preferences and settings
  preferences: UserPreferences;
  
  // Analytics data
  purchasePatterns: PurchasePattern[];
  
  // Membership and loyalty
  membershipType: 'free' | 'premium';
  membershipExpiry?: Date;
  loyaltyPoints: number;
  
  // Notification settings
  pushToken?: string;
  emailNotifications: boolean;
  smsNotifications: boolean;
  
  // Account metadata
  createdAt: Date;
  lastLoginAt: Date;
  isActive: boolean;
  isVerified: boolean;
}

export interface Address {
  id: string;
  userId: string;
  type: 'home' | 'work' | 'other';
  label: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  pincode: string;
  landmark?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isDefault: boolean;
  deliveryInstructions?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'price_drop' | 'stock_alert' | 'order_update' | 'promotion' | 'system';
  title: string;
  body: string;
  data?: {
    screen?: string;
    params?: Record<string, any>;
    productId?: string;
    orderId?: string;
    oldPrice?: string;
    newPrice?: string;
    dropPercent?: string;
    dealCount?: string;
    totalSavings?: string;
    milestone?: string;
    reward?: string;
    summary?: string;
    totalScans?: string;
    newProducts?: string;
    topCategory?: string;
    savings?: string;
    [key: string]: any; // Allow additional properties
  };
  read: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

export interface UserSession {
  id: string;
  userId: string;
  deviceId: string;
  deviceType: 'ios' | 'android' | 'web';
  pushToken?: string;
  lastActiveAt: Date;
  createdAt: Date;
}

// Authentication types
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  membershipType: 'free' | 'premium';
  isVerified: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  phone: string;
  password: string;
}

export interface OTPVerification {
  phone: string;
  otp: string;
}

// User activity tracking
export interface UserActivity {
  id: string;
  userId: string;
  action: 'login' | 'logout' | 'product_view' | 'add_to_cart' | 'purchase' | 'search' | 'scan_barcode';
  metadata?: {
    productId?: string;
    searchQuery?: string;
    barcode?: string;
    orderId?: string;
    deviceInfo?: {
      platform: string;
      version: string;
    };
  };
  timestamp: Date;
}

// Group buying types
export interface GroupBuy {
  id: string;
  creatorId: string;
  code: string;
  name: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  discountTiers: DiscountTier[];
  participants: GroupParticipant[];
  deliveryAddress: Address;
  status: 'active' | 'completed' | 'cancelled' | 'expired';
  expiresAt: Date;
  createdAt: Date;
}

export interface DiscountTier {
  minAmount: number;
  discountPercent: number;
}

export interface GroupParticipant {
  userId: string;
  user: Pick<User, 'id' | 'name' | 'avatar'>;
  joinedAt: Date;
  contribution: number;
  items: {
    productId: string;
    quantity: number;
    price: number;
  }[];
}

// Recurring delivery types
export interface RecurringDelivery {
  id: string;
  userId: string;
  name: string;
  frequency: 'weekly' | 'biweekly' | 'monthly';
  dayOfWeek?: number; // 0-6 for weekly/biweekly
  dayOfMonth?: number; // 1-31 for monthly
  timeSlot: string;
  addressId: string;
  items: {
    productId: string;
    quantity: number;
  }[];
  nextDeliveryDate: Date;
  isActive: boolean;
  isPaused: boolean;
  pausedUntil?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Budget tracking types
export interface BudgetSettings {
  id: string;
  userId: string;
  totalBudget: number;
  period: 'weekly' | 'monthly';
  categories: BudgetCategory[];
  alertThreshold: number; // Percentage (e.g., 80 for 80%)
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface BudgetCategory {
  categoryId: string;
  categoryName: string;
  allocatedAmount: number;
  spentAmount: number;
  percentage: number;
}

export interface BudgetAlert {
  id: string;
  userId: string;
  budgetId: string;
  type: 'category_exceeded' | 'total_exceeded' | 'threshold_reached';
  message: string;
  amount: number;
  threshold: number;
  createdAt: Date;
  acknowledged: boolean;
}
