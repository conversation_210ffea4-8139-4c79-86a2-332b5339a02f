// Price tracking service for monitoring product prices and detecting drops

import { PriceAlert, PricePoint, Product, WatchedProduct } from '@/types/product';
import { User } from '@/types/user';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { sendPushNotification } from './notifications';

// Storage keys
const PRICE_HISTORY_KEY = 'price_history';
const WATCHED_PRODUCTS_KEY = 'watched_products';
const PRICE_ALERTS_KEY = 'price_alerts';

// Mock product data for demonstration
const MOCK_PRODUCTS: Product[] = [
  {
    id: 'p1',
    name: 'Organic Bananas',
    description: 'Fresh organic bananas, 1kg',
    price: 49,
    originalPrice: 99,
    category: 'fruits',
    brand: 'Organic Valley',
    weight: '1kg',
    imageUrl: '🍌',
    inStock: true,
    stockQuantity: 50,
    barcode: '1234567890123',
    priceHistory: [],
    lowestPrice: 45,
    highestPrice: 99,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'p2',
    name: 'Fresh Milk 1L',
    description: 'Pure cow milk, 1 liter',
    price: 59,
    originalPrice: 89,
    category: 'dairy',
    brand: 'Amul',
    weight: '1L',
    imageUrl: '🥛',
    inStock: true,
    stockQuantity: 30,
    barcode: '2345678901234',
    priceHistory: [],
    lowestPrice: 55,
    highestPrice: 89,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'p3',
    name: 'Whole Wheat Bread',
    description: 'Freshly baked whole wheat bread',
    price: 35,
    originalPrice: 45,
    category: 'bakery',
    brand: 'Britannia',
    weight: '400g',
    imageUrl: '🍞',
    inStock: true,
    stockQuantity: 25,
    barcode: '3456789012345',
    priceHistory: [],
    lowestPrice: 32,
    highestPrice: 45,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'p4',
    name: 'Tomatoes 1kg',
    description: 'Fresh red tomatoes, 1kg',
    price: 49,
    originalPrice: 80,
    category: 'vegetables',
    brand: 'Farm Fresh',
    weight: '1kg',
    imageUrl: '🍅',
    inStock: true,
    stockQuantity: 40,
    barcode: '4567890123456',
    priceHistory: [],
    lowestPrice: 45,
    highestPrice: 80,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

/**
 * Track daily prices for all products
 * This would typically be called by a scheduled job
 */
export async function trackDailyPrices(): Promise<void> {
  try {
    console.log('Starting daily price tracking...');
    
    // Get current products (in a real app, this would fetch from your API)
    const products = await getAllProducts();
    
    for (const product of products) {
      // Add today's price to history
      const pricePoint: PricePoint = {
        price: product.price,
        date: new Date(),
      };
      
      // Update product price history
      product.priceHistory.push(pricePoint);
      
      // Keep only last 90 days of history to manage storage
      if (product.priceHistory.length > 90) {
        product.priceHistory = product.priceHistory.slice(-90);
      }
      
      // Update lowest and highest prices
      const prices = product.priceHistory.map(p => p.price);
      product.lowestPrice = Math.min(...prices);
      product.highestPrice = Math.max(...prices);
      
      // Save updated product
      await saveProduct(product);
    }
    
    console.log(`Updated price history for ${products.length} products`);
    
    // After tracking prices, check for price drops
    await detectPriceDrops();
    
  } catch (error) {
    console.error('Error tracking daily prices:', error);
  }
}

/**
 * Detect price drops and send notifications
 */
export async function detectPriceDrops(): Promise<void> {
  try {
    console.log('Detecting price drops...');
    
    const products = await getAllProducts();
    const users = await getAllUsers(); // This would fetch from your user database
    
    for (const product of products) {
      // Need at least 2 price points to detect a drop
      if (product.priceHistory.length < 2) continue;
      
      // Sort by date descending to get latest prices
      const sortedHistory = [...product.priceHistory].sort((a, b) => 
        new Date(b.date).getTime() - new Date(a.date).getTime()
      );
      
      const currentPrice = sortedHistory[0].price;
      const previousPrice = sortedHistory[1].price;
      
      // Check if price dropped
      if (currentPrice < previousPrice) {
        const dropPercent = ((previousPrice - currentPrice) / previousPrice) * 100;
        
        // Find users watching this product
        for (const user of users) {
          const watchedProduct = user.watchedProducts?.find(wp => 
            wp.productId === product.id
          );
          
          if (watchedProduct) {
            // Check if we should notify based on user preferences
            const shouldNotify = watchedProduct.notifyOnAnyDrop || 
              (watchedProduct.targetPrice && currentPrice <= watchedProduct.targetPrice) ||
              (watchedProduct.dropThreshold && dropPercent >= watchedProduct.dropThreshold);
            
            if (shouldNotify) {
              await sendPriceDropNotification(
                user.id,
                product,
                previousPrice,
                currentPrice,
                dropPercent
              );
            }
          }
        }
      }
    }
    
    console.log('Price drop detection completed');
    
  } catch (error) {
    console.error('Error detecting price drops:', error);
  }
}

/**
 * Send price drop notification to user
 */
async function sendPriceDropNotification(
  userId: string,
  product: Product,
  oldPrice: number,
  newPrice: number,
  dropPercent: number
): Promise<void> {
  try {
    const title = 'Price Drop Alert! 📉';
    const body = `${product.name} is now ₹${newPrice} (was ₹${oldPrice}, ${Math.round(dropPercent)}% off)`;
    
    // Create price alert record
    const priceAlert: PriceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      productId: product.id,
      oldPrice,
      newPrice,
      dropPercent,
      notificationSent: true,
      createdAt: new Date(),
    };
    
    // Save price alert
    await savePriceAlert(priceAlert);
    
    // Send push notification (if user has push token)
    const user = await getUserById(userId);
    if (user?.pushToken) {
      await sendPushNotification(user.pushToken, {
        title,
        body,
        data: {
          screen: 'product-details',
          params: { productId: product.id }
        }
      });
    }
    
    console.log(`Price drop notification sent to user ${userId} for product ${product.name}`);
    
  } catch (error) {
    console.error('Error sending price drop notification:', error);
  }
}

/**
 * Add product to user's watchlist
 */
export async function addToWatchlist(
  userId: string, 
  productId: string, 
  options: {
    targetPrice?: number;
    notifyOnAnyDrop?: boolean;
    dropThreshold?: number;
  } = {}
): Promise<void> {
  try {
    const user = await getUserById(userId);
    if (!user) throw new Error('User not found');
    
    // Check if product is already being watched
    const existingIndex = user.watchedProducts.findIndex(wp => wp.productId === productId);
    
    const watchedProduct: WatchedProduct = {
      productId,
      targetPrice: options.targetPrice,
      notifyOnAnyDrop: options.notifyOnAnyDrop ?? true,
      dropThreshold: options.dropThreshold ?? 5, // Default 5% threshold
      addedAt: new Date(),
    };
    
    if (existingIndex >= 0) {
      // Update existing watched product
      user.watchedProducts[existingIndex] = watchedProduct;
    } else {
      // Add new watched product
      user.watchedProducts.push(watchedProduct);
    }
    
    await saveUser(user);
    console.log(`Product ${productId} added to watchlist for user ${userId}`);
    
  } catch (error) {
    console.error('Error adding to watchlist:', error);
    throw error;
  }
}

/**
 * Remove product from user's watchlist
 */
export async function removeFromWatchlist(userId: string, productId: string): Promise<void> {
  try {
    const user = await getUserById(userId);
    if (!user) throw new Error('User not found');
    
    user.watchedProducts = user.watchedProducts.filter(wp => wp.productId !== productId);
    
    await saveUser(user);
    console.log(`Product ${productId} removed from watchlist for user ${userId}`);
    
  } catch (error) {
    console.error('Error removing from watchlist:', error);
    throw error;
  }
}

/**
 * Get user's watched products with product details
 */
export async function getWatchedProducts(userId: string): Promise<(Product & { watchedAt: Date })[]> {
  try {
    const user = await getUserById(userId);
    if (!user) return [];
    
    const watchedProducts = [];
    
    for (const watchedProduct of user.watchedProducts) {
      const product = await getProductById(watchedProduct.productId);
      if (product) {
        watchedProducts.push({
          ...product,
          watchedAt: watchedProduct.addedAt,
        });
      }
    }
    
    return watchedProducts;
    
  } catch (error) {
    console.error('Error getting watched products:', error);
    return [];
  }
}

/**
 * Get price history for a product
 */
export async function getPriceHistory(productId: string, days: number = 30): Promise<PricePoint[]> {
  try {
    const product = await getProductById(productId);
    if (!product) return [];
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return product.priceHistory
      .filter(point => new Date(point.date) >= cutoffDate)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
  } catch (error) {
    console.error('Error getting price history:', error);
    return [];
  }
}

// Storage helper functions (in a real app, these would interact with your backend API)

async function getAllProducts(): Promise<Product[]> {
  try {
    const stored = await AsyncStorage.getItem('products');
    if (stored) {
      return JSON.parse(stored);
    }
    
    // Initialize with mock data if no products exist
    await AsyncStorage.setItem('products', JSON.stringify(MOCK_PRODUCTS));
    return MOCK_PRODUCTS;
  } catch (error) {
    console.error('Error getting products:', error);
    return MOCK_PRODUCTS;
  }
}

async function getProductById(productId: string): Promise<Product | null> {
  try {
    const products = await getAllProducts();
    return products.find(p => p.id === productId) || null;
  } catch (error) {
    console.error('Error getting product by ID:', error);
    return null;
  }
}

async function saveProduct(product: Product): Promise<void> {
  try {
    const products = await getAllProducts();
    const index = products.findIndex(p => p.id === product.id);
    
    if (index >= 0) {
      products[index] = product;
    } else {
      products.push(product);
    }
    
    await AsyncStorage.setItem('products', JSON.stringify(products));
  } catch (error) {
    console.error('Error saving product:', error);
  }
}

async function getAllUsers(): Promise<User[]> {
  try {
    const stored = await AsyncStorage.getItem('users');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting users:', error);
    return [];
  }
}

async function getUserById(userId: string): Promise<User | null> {
  try {
    const users = await getAllUsers();
    return users.find(u => u.id === userId) || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

async function saveUser(user: User): Promise<void> {
  try {
    const users = await getAllUsers();
    const index = users.findIndex(u => u.id === user.id);
    
    if (index >= 0) {
      users[index] = user;
    } else {
      users.push(user);
    }
    
    await AsyncStorage.setItem('users', JSON.stringify(users));
  } catch (error) {
    console.error('Error saving user:', error);
  }
}

async function savePriceAlert(alert: PriceAlert): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(PRICE_ALERTS_KEY);
    const alerts: PriceAlert[] = stored ? JSON.parse(stored) : [];
    
    alerts.push(alert);
    
    // Keep only last 100 alerts to manage storage
    if (alerts.length > 100) {
      alerts.splice(0, alerts.length - 100);
    }
    
    await AsyncStorage.setItem(PRICE_ALERTS_KEY, JSON.stringify(alerts));
  } catch (error) {
    console.error('Error saving price alert:', error);
  }
}

/**
 * Initialize price tracking with some sample data
 */
export async function initializePriceTracking(): Promise<void> {
  try {
    // Check if we already have products
    const products = await getAllProducts();
    
    // Add some sample price history for demonstration
    for (const product of products) {
      if (product.priceHistory.length === 0) {
        // Generate 30 days of sample price history
        const history: PricePoint[] = [];
        const basePrice = product.price;
        
        for (let i = 29; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          
          // Add some random price variation (±20%)
          const variation = (Math.random() - 0.5) * 0.4; // -20% to +20%
          const price = Math.round(basePrice * (1 + variation));
          
          history.push({
            price: Math.max(price, Math.round(basePrice * 0.7)), // Don't go below 70% of base price
            date,
          });
        }
        
        product.priceHistory = history;
        
        // Update lowest and highest prices
        const prices = history.map(p => p.price);
        product.lowestPrice = Math.min(...prices);
        product.highestPrice = Math.max(...prices);
        
        await saveProduct(product);
      }
    }
    
    console.log('Price tracking initialized with sample data');
    
  } catch (error) {
    console.error('Error initializing price tracking:', error);
  }
}
