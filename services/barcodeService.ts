// Barcode scanning service for product lookup and scan history management

import { Product, ScanHistory } from '@/types/product';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const SCAN_HISTORY_KEY = 'scan_history';
const PRODUCTS_KEY = 'products';

// Mock product database with barcodes for demonstration
const MOCK_PRODUCTS: Product[] = [
  {
    id: 'p1',
    name: 'Organic Bananas',
    description: 'Fresh organic bananas, 1kg pack',
    price: 49,
    originalPrice: 99,
    discount: 50,
    category: 'fruits',
    brand: 'Organic Valley',
    weight: '1kg',
    imageUrl: '🍌',
    inStock: true,
    stockQuantity: 50,
    barcode: '1234567890123',
    priceHistory: [
      { price: 99, date: new Date('2024-01-01') },
      { price: 79, date: new Date('2024-01-15') },
      { price: 59, date: new Date('2024-02-01') },
      { price: 49, date: new Date('2024-02-15') },
    ],
    lowestPrice: 45,
    highestPrice: 99,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: 'p2',
    name: 'Fresh Milk 1L',
    description: 'Pure cow milk, 1 liter pack',
    price: 59,
    originalPrice: 89,
    discount: 34,
    category: 'dairy',
    brand: 'Amul',
    weight: '1L',
    imageUrl: '🥛',
    inStock: true,
    stockQuantity: 30,
    barcode: '2345678901234',
    priceHistory: [
      { price: 89, date: new Date('2024-01-01') },
      { price: 79, date: new Date('2024-01-15') },
      { price: 69, date: new Date('2024-02-01') },
      { price: 59, date: new Date('2024-02-15') },
    ],
    lowestPrice: 55,
    highestPrice: 89,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: 'p3',
    name: 'Whole Wheat Bread',
    description: 'Fresh baked whole wheat bread, 400g',
    price: 35,
    originalPrice: 45,
    discount: 22,
    category: 'bakery',
    brand: 'Harvest Gold',
    weight: '400g',
    imageUrl: '🍞',
    inStock: true,
    stockQuantity: 25,
    barcode: '3456789012345',
    priceHistory: [
      { price: 45, date: new Date('2024-01-01') },
      { price: 42, date: new Date('2024-01-15') },
      { price: 38, date: new Date('2024-02-01') },
      { price: 35, date: new Date('2024-02-15') },
    ],
    lowestPrice: 32,
    highestPrice: 45,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: 'p4',
    name: 'Basmati Rice 5kg',
    description: 'Premium basmati rice, 5kg pack',
    price: 299,
    originalPrice: 399,
    discount: 25,
    category: 'grains',
    brand: 'India Gate',
    weight: '5kg',
    imageUrl: '🍚',
    inStock: true,
    stockQuantity: 15,
    barcode: '4567890123456',
    priceHistory: [
      { price: 399, date: new Date('2024-01-01') },
      { price: 359, date: new Date('2024-01-15') },
      { price: 329, date: new Date('2024-02-01') },
      { price: 299, date: new Date('2024-02-15') },
    ],
    lowestPrice: 289,
    highestPrice: 399,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
  {
    id: 'p5',
    name: 'Olive Oil 500ml',
    description: 'Extra virgin olive oil, 500ml bottle',
    price: 189,
    originalPrice: 249,
    discount: 24,
    category: 'oils',
    brand: 'Figaro',
    weight: '500ml',
    imageUrl: '🫒',
    inStock: true,
    stockQuantity: 20,
    barcode: '5678901234567',
    priceHistory: [
      { price: 249, date: new Date('2024-01-01') },
      { price: 229, date: new Date('2024-01-15') },
      { price: 209, date: new Date('2024-02-01') },
      { price: 189, date: new Date('2024-02-15') },
    ],
    lowestPrice: 179,
    highestPrice: 249,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
  },
];

/**
 * Find product by barcode
 */
export async function findProductByBarcode(barcode: string): Promise<Product | null> {
  try {
    console.log('Looking up product with barcode:', barcode);
    
    // In a real app, this would be an API call to your backend
    // For demo purposes, we'll use the mock data
    const product = MOCK_PRODUCTS.find(p => p.barcode === barcode);
    
    if (product) {
      console.log('Product found:', product.name);
      return product;
    }
    
    // Try to get from stored products (in case of new products added)
    const storedProducts = await getStoredProducts();
    const storedProduct = storedProducts.find(p => p.barcode === barcode);
    
    if (storedProduct) {
      console.log('Product found in storage:', storedProduct.name);
      return storedProduct;
    }
    
    console.log('Product not found for barcode:', barcode);
    return null;
    
  } catch (error) {
    console.error('Error finding product by barcode:', error);
    return null;
  }
}

/**
 * Add barcode scan to history
 */
export async function addToScanHistory(userId: string, barcode: string): Promise<void> {
  try {
    const product = await findProductByBarcode(barcode);
    
    const scanRecord: ScanHistory = {
      id: `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      barcode,
      productId: product?.id || '',
      timestamp: new Date(),
    };
    
    const stored = await AsyncStorage.getItem(SCAN_HISTORY_KEY);
    const scanHistory: ScanHistory[] = stored ? JSON.parse(stored) : [];
    
    // Add new scan to the beginning
    scanHistory.unshift(scanRecord);
    
    // Keep only last 100 scans per user
    const userScans = scanHistory.filter(scan => scan.userId === userId);
    if (userScans.length > 100) {
      const toRemove = userScans.slice(100);
      toRemove.forEach(scan => {
        const index = scanHistory.findIndex(s => s.id === scan.id);
        if (index >= 0) {
          scanHistory.splice(index, 1);
        }
      });
    }
    
    await AsyncStorage.setItem(SCAN_HISTORY_KEY, JSON.stringify(scanHistory));
    console.log('Scan added to history:', barcode);
    
  } catch (error) {
    console.error('Error adding to scan history:', error);
  }
}

/**
 * Get scan history for user
 */
export async function getScanHistory(userId: string): Promise<ScanHistory[]> {
  try {
    const stored = await AsyncStorage.getItem(SCAN_HISTORY_KEY);
    const scanHistory: ScanHistory[] = stored ? JSON.parse(stored) : [];
    
    return scanHistory
      .filter(scan => scan.userId === userId)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
  } catch (error) {
    console.error('Error getting scan history:', error);
    return [];
  }
}

/**
 * Get stored products
 */
async function getStoredProducts(): Promise<Product[]> {
  try {
    const stored = await AsyncStorage.getItem(PRODUCTS_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting stored products:', error);
    return [];
  }
}

/**
 * Clear scan history for user
 */
export async function clearScanHistory(userId: string): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(SCAN_HISTORY_KEY);
    const scanHistory: ScanHistory[] = stored ? JSON.parse(stored) : [];
    
    // Remove all scans for this user
    const filteredHistory = scanHistory.filter(scan => scan.userId !== userId);
    
    await AsyncStorage.setItem(SCAN_HISTORY_KEY, JSON.stringify(filteredHistory));
    console.log('Scan history cleared for user:', userId);
    
  } catch (error) {
    console.error('Error clearing scan history:', error);
  }
}

/**
 * Get recent scanned products for user
 */
export async function getRecentScannedProducts(userId: string, limit: number = 10): Promise<Product[]> {
  try {
    const scanHistory = await getScanHistory(userId);
    const recentProducts: Product[] = [];
    const seenProductIds = new Set<string>();
    
    for (const scan of scanHistory) {
      if (recentProducts.length >= limit) break;
      
      if (scan.productId && !seenProductIds.has(scan.productId)) {
        const product = await findProductByBarcode(scan.barcode);
        if (product) {
          recentProducts.push(product);
          seenProductIds.add(scan.productId);
        }
      }
    }
    
    return recentProducts;
    
  } catch (error) {
    console.error('Error getting recent scanned products:', error);
    return [];
  }
}

/**
 * Get scan statistics for user
 */
export async function getScanStatistics(userId: string): Promise<{
  totalScans: number;
  uniqueProducts: number;
  todayScans: number;
  weekScans: number;
}> {
  try {
    const scanHistory = await getScanHistory(userId);
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    const uniqueProducts = new Set(scanHistory.map(scan => scan.productId)).size;
    const todayScans = scanHistory.filter(scan => 
      new Date(scan.timestamp) >= todayStart
    ).length;
    const weekScans = scanHistory.filter(scan => 
      new Date(scan.timestamp) >= weekAgo
    ).length;
    
    return {
      totalScans: scanHistory.length,
      uniqueProducts,
      todayScans,
      weekScans,
    };
    
  } catch (error) {
    console.error('Error getting scan statistics:', error);
    return {
      totalScans: 0,
      uniqueProducts: 0,
      todayScans: 0,
      weekScans: 0,
    };
  }
}
