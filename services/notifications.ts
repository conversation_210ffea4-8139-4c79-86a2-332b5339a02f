// Notification service for sending push notifications and managing in-app notifications

import { Notification } from '@/types/user';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Storage key for in-app notifications
const NOTIFICATIONS_KEY = 'app_notifications';

export interface PushNotificationData {
  title: string;
  body: string;
  data?: {
    screen?: string;
    params?: Record<string, any>;
    productId?: string;
    orderId?: string;
  };
}

/**
 * Request notification permissions
 */
export async function requestNotificationPermissions(): Promise<boolean> {
  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.log('Notification permission not granted');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
}

/**
 * Get the device's push token
 */
export async function getPushToken(): Promise<string | null> {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      return null;
    }
    
    const token = await Notifications.getExpoPushTokenAsync({
      projectId: 'your-expo-project-id', // Replace with your actual project ID
    });
    
    return token.data;
  } catch (error) {
    console.error('Error getting push token:', error);
    return null;
  }
}

/**
 * Send a push notification
 */
export async function sendPushNotification(
  pushToken: string,
  notificationData: PushNotificationData
): Promise<boolean> {
  try {
    const message = {
      to: pushToken,
      sound: 'default',
      title: notificationData.title,
      body: notificationData.body,
      data: notificationData.data || {},
    };
    
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    const result = await response.json();
    
    if (result.data && result.data.status === 'ok') {
      console.log('Push notification sent successfully');
      return true;
    } else {
      console.error('Failed to send push notification:', result);
      return false;
    }
  } catch (error) {
    console.error('Error sending push notification:', error);
    return false;
  }
}

/**
 * Schedule a local notification
 */
export async function scheduleLocalNotification(
  notificationData: PushNotificationData,
  trigger?: Notifications.NotificationTriggerInput
): Promise<string | null> {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      return null;
    }
    
    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: notificationData.title,
        body: notificationData.body,
        data: notificationData.data || {},
        sound: 'default',
      },
      trigger: trigger || null, // null means immediate
    });
    
    console.log('Local notification scheduled:', notificationId);
    return notificationId;
  } catch (error) {
    console.error('Error scheduling local notification:', error);
    return null;
  }
}

/**
 * Cancel a scheduled notification
 */
export async function cancelNotification(notificationId: string): Promise<void> {
  try {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
    console.log('Notification cancelled:', notificationId);
  } catch (error) {
    console.error('Error cancelling notification:', error);
  }
}

/**
 * Cancel all scheduled notifications
 */
export async function cancelAllNotifications(): Promise<void> {
  try {
    await Notifications.cancelAllScheduledNotificationsAsync();
    console.log('All notifications cancelled');
  } catch (error) {
    console.error('Error cancelling all notifications:', error);
  }
}

/**
 * Save in-app notification
 */
export async function saveInAppNotification(
  userId: string,
  type: Notification['type'],
  title: string,
  body: string,
  data?: Notification['data']
): Promise<void> {
  try {
    const notification: Notification = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      type,
      title,
      body,
      data,
      read: false,
      createdAt: new Date(),
    };
    
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    notifications.unshift(notification); // Add to beginning
    
    // Keep only last 100 notifications per user
    const userNotifications = notifications.filter(n => n.userId === userId);
    if (userNotifications.length > 100) {
      const toRemove = userNotifications.slice(100);
      toRemove.forEach(notif => {
        const index = notifications.findIndex(n => n.id === notif.id);
        if (index >= 0) {
          notifications.splice(index, 1);
        }
      });
    }
    
    await AsyncStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(notifications));
    console.log('In-app notification saved');
  } catch (error) {
    console.error('Error saving in-app notification:', error);
  }
}

/**
 * Get user's in-app notifications
 */
export async function getUserNotifications(userId: string): Promise<Notification[]> {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    return notifications
      .filter(n => n.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return [];
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId: string): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      await AsyncStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(notifications));
      console.log('Notification marked as read:', notificationId);
    }
  } catch (error) {
    console.error('Error marking notification as read:', error);
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: string): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    let updated = false;
    notifications.forEach(notification => {
      if (notification.userId === userId && !notification.read) {
        notification.read = true;
        updated = true;
      }
    });
    
    if (updated) {
      await AsyncStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(notifications));
      console.log('All notifications marked as read for user:', userId);
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
  }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: string): Promise<number> {
  try {
    const notifications = await getUserNotifications(userId);
    return notifications.filter(n => !n.read).length;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return 0;
  }
}

/**
 * Delete notification
 */
export async function deleteNotification(notificationId: string): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    const filteredNotifications = notifications.filter(n => n.id !== notificationId);
    
    await AsyncStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(filteredNotifications));
    console.log('Notification deleted:', notificationId);
  } catch (error) {
    console.error('Error deleting notification:', error);
  }
}

/**
 * Clear old notifications (older than 30 days)
 */
export async function clearOldNotifications(): Promise<void> {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATIONS_KEY);
    const notifications: Notification[] = stored ? JSON.parse(stored) : [];
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const filteredNotifications = notifications.filter(
      n => new Date(n.createdAt) > thirtyDaysAgo
    );
    
    if (filteredNotifications.length !== notifications.length) {
      await AsyncStorage.setItem(NOTIFICATIONS_KEY, JSON.stringify(filteredNotifications));
      console.log(`Cleared ${notifications.length - filteredNotifications.length} old notifications`);
    }
  } catch (error) {
    console.error('Error clearing old notifications:', error);
  }
}

/**
 * Setup notification listeners
 */
export function setupNotificationListeners() {
  // Listen for notifications received while app is in foreground
  const foregroundSubscription = Notifications.addNotificationReceivedListener((notification: any) => {
    console.log('Notification received in foreground:', notification);
    // You can handle foreground notifications here
  });
  
  // Listen for notification responses (when user taps notification)
  const responseSubscription = Notifications.addNotificationResponseReceivedListener((response: any) => {
    console.log('Notification response received:', response);
    
    const data = response.notification.request.content.data;
    
    // Handle navigation based on notification data
    if (data.screen) {
      // Navigate to the specified screen
      // This would typically use your navigation service
      console.log('Navigate to:', data.screen, 'with params:', data.params);
    }
  });
  
  return () => {
    foregroundSubscription.remove();
    responseSubscription.remove();
  };
}

/**
 * Send price drop notification (specific helper for price alerts)
 */
export async function sendPriceDropAlert(
  userId: string,
  pushToken: string | undefined,
  productName: string,
  oldPrice: number,
  newPrice: number,
  dropPercent: number,
  productId: string
): Promise<void> {
  const title = 'Price Drop Alert! 📉';
  const body = `${productName} is now ₹${newPrice} (was ₹${oldPrice}, ${Math.round(dropPercent)}% off)`;
  
  // Save in-app notification
  await saveInAppNotification(
    userId,
    'price_drop',
    title,
    body,
    {
      screen: 'product-details',
      params: { productId },
      productId,
    }
  );
  
  // Send push notification if user has push token
  if (pushToken) {
    await sendPushNotification(pushToken, {
      title,
      body,
      data: {
        screen: 'product-details',
        params: { productId },
        productId,
      },
    });
  }
}

/**
 * Send stock alert notification
 */
export async function sendStockAlert(
  userId: string,
  pushToken: string | undefined,
  productName: string,
  productId: string
): Promise<void> {
  const title = 'Back in Stock! 📦';
  const body = `${productName} is now available for order`;
  
  // Save in-app notification
  await saveInAppNotification(
    userId,
    'stock_alert',
    title,
    body,
    {
      screen: 'product-details',
      params: { productId },
      productId,
    }
  );
  
  // Send push notification if user has push token
  if (pushToken) {
    await sendPushNotification(pushToken, {
      title,
      body,
      data: {
        screen: 'product-details',
        params: { productId },
        productId,
      },
    });
  }
}

/**
 * Send order update notification
 */
export async function sendOrderUpdate(
  userId: string,
  pushToken: string | undefined,
  orderId: string,
  status: string,
  message: string
): Promise<void> {
  const title = `Order ${status}`;
  const body = message;
  
  // Save in-app notification
  await saveInAppNotification(
    userId,
    'order_update',
    title,
    body,
    {
      screen: 'order-details',
      params: { orderId },
      orderId,
    }
  );
  
  // Send push notification if user has push token
  if (pushToken) {
    await sendPushNotification(pushToken, {
      title,
      body,
      data: {
        screen: 'order-details',
        params: { orderId },
        orderId,
      },
    });
  }
}
