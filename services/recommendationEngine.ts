// Advanced recommendation engine based on scan history and user behavior

import { Product, ScanHistory } from '@/types/product';
import { getScanHistory, findProductByBarcode } from './barcodeService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const USER_PREFERENCES_KEY = 'user_preferences';
const RECOMMENDATION_CACHE_KEY = 'recommendation_cache';

interface UserPreferences {
  userId: string;
  favoriteCategories: Array<{ category: string; score: number }>;
  favoriteBrands: Array<{ brand: string; score: number }>;
  priceRange: { min: number; max: number };
  scanPatterns: {
    peakHours: number[];
    frequentDays: string[];
    averageSessionLength: number;
  };
  lastUpdated: Date;
}

interface RecommendationScore {
  productId: string;
  score: number;
  reasons: string[];
  confidence: number;
}

interface ProductRecommendation {
  product: Product;
  score: number;
  reasons: string[];
  confidence: number;
  type: 'similar' | 'complementary' | 'trending' | 'personalized' | 'price_drop';
}

/**
 * Generate personalized product recommendations
 */
export async function generateRecommendations(
  userId: string,
  limit: number = 10,
  excludeProductIds: string[] = []
): Promise<ProductRecommendation[]> {
  try {
    console.log(`Generating recommendations for user ${userId}`);
    
    // Get user preferences and scan history
    const preferences = await getUserPreferences(userId);
    const scanHistory = await getScanHistory(userId);
    
    // Get all available products (in real app, this would be from API)
    const allProducts = await getAllProducts();
    
    // Calculate recommendation scores
    const scores = await calculateRecommendationScores(
      userId,
      allProducts,
      preferences,
      scanHistory,
      excludeProductIds
    );
    
    // Sort by score and take top recommendations
    const topScores = scores
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
    
    // Convert to full recommendations
    const recommendations: ProductRecommendation[] = [];
    
    for (const scoreData of topScores) {
      const product = allProducts.find(p => p.id === scoreData.productId);
      if (product) {
        recommendations.push({
          product,
          score: scoreData.score,
          reasons: scoreData.reasons,
          confidence: scoreData.confidence,
          type: determineRecommendationType(scoreData, preferences, scanHistory),
        });
      }
    }
    
    // Cache recommendations
    await cacheRecommendations(userId, recommendations);
    
    console.log(`Generated ${recommendations.length} recommendations`);
    return recommendations;
    
  } catch (error) {
    console.error('Error generating recommendations:', error);
    return [];
  }
}

/**
 * Get recommendations based on a specific product (similar/complementary)
 */
export async function getProductBasedRecommendations(
  productId: string,
  userId: string,
  limit: number = 5
): Promise<ProductRecommendation[]> {
  try {
    const allProducts = await getAllProducts();
    const targetProduct = allProducts.find(p => p.id === productId);
    
    if (!targetProduct) return [];
    
    const preferences = await getUserPreferences(userId);
    const recommendations: ProductRecommendation[] = [];
    
    // Find similar products (same category, similar price range)
    const similarProducts = allProducts.filter(product => 
      product.id !== productId &&
      product.category === targetProduct.category &&
      Math.abs(product.price - targetProduct.price) <= targetProduct.price * 0.5
    );
    
    // Find complementary products (products often bought together)
    const complementaryProducts = await getComplementaryProducts(targetProduct, allProducts);
    
    // Score and combine results
    const combinedProducts = [...similarProducts, ...complementaryProducts];
    
    for (const product of combinedProducts.slice(0, limit)) {
      const isSimilar = similarProducts.includes(product);
      const score = calculateProductSimilarityScore(targetProduct, product, preferences);
      
      recommendations.push({
        product,
        score,
        reasons: isSimilar 
          ? [`Similar to ${targetProduct.name}`, `Same category: ${product.category}`]
          : [`Often bought with ${targetProduct.name}`, 'Complementary product'],
        confidence: isSimilar ? 0.8 : 0.6,
        type: isSimilar ? 'similar' : 'complementary',
      });
    }
    
    return recommendations.sort((a, b) => b.score - a.score);
    
  } catch (error) {
    console.error('Error getting product-based recommendations:', error);
    return [];
  }
}

/**
 * Get trending products based on scan frequency
 */
export async function getTrendingProducts(
  userId: string,
  limit: number = 5
): Promise<ProductRecommendation[]> {
  try {
    // In a real app, this would analyze global scan data
    // For demo, we'll use mock trending data
    const allProducts = await getAllProducts();
    const preferences = await getUserPreferences(userId);
    
    // Mock trending algorithm - products with high scan frequency
    const trendingProductIds = ['p1', 'p2', 'p5']; // Mock trending products
    
    const recommendations: ProductRecommendation[] = [];
    
    for (const productId of trendingProductIds.slice(0, limit)) {
      const product = allProducts.find(p => p.id === productId);
      if (product) {
        const categoryScore = preferences.favoriteCategories
          .find(cat => cat.category === product.category)?.score || 0;
        
        recommendations.push({
          product,
          score: 0.7 + (categoryScore * 0.3),
          reasons: ['Trending now', 'Popular with other users'],
          confidence: 0.7,
          type: 'trending',
        });
      }
    }
    
    return recommendations;
    
  } catch (error) {
    console.error('Error getting trending products:', error);
    return [];
  }
}

/**
 * Calculate recommendation scores for all products
 */
async function calculateRecommendationScores(
  userId: string,
  products: Product[],
  preferences: UserPreferences,
  scanHistory: ScanHistory[],
  excludeProductIds: string[]
): Promise<RecommendationScore[]> {
  const scores: RecommendationScore[] = [];
  
  for (const product of products) {
    if (excludeProductIds.includes(product.id)) continue;
    
    const reasons: string[] = [];
    let score = 0;
    
    // Category preference score (0-0.3)
    const categoryPref = preferences.favoriteCategories
      .find(cat => cat.category === product.category);
    if (categoryPref) {
      score += categoryPref.score * 0.3;
      reasons.push(`You like ${product.category} products`);
    }
    
    // Brand preference score (0-0.2)
    const brandPref = preferences.favoriteBrands
      .find(brand => brand.brand === product.brand);
    if (brandPref) {
      score += brandPref.score * 0.2;
      reasons.push(`You like ${product.brand} brand`);
    }
    
    // Price range score (0-0.2)
    if (product.price >= preferences.priceRange.min && 
        product.price <= preferences.priceRange.max) {
      score += 0.2;
      reasons.push('Within your price range');
    }
    
    // Discount score (0-0.15)
    if (product.discount && product.discount > 0) {
      score += Math.min(product.discount / 100, 0.15);
      reasons.push(`${product.discount}% discount available`);
    }
    
    // Stock availability score (0-0.1)
    if (product.inStock) {
      score += 0.1;
    } else {
      score -= 0.2; // Penalty for out of stock
      reasons.push('Currently out of stock');
    }
    
    // Novelty score - prefer products not recently scanned (0-0.05)
    const recentlyScanned = scanHistory.some(scan => 
      scan.productId === product.id && 
      new Date(scan.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );
    if (!recentlyScanned) {
      score += 0.05;
    }
    
    // Calculate confidence based on available data
    const confidence = Math.min(
      (reasons.length / 5) + (scanHistory.length > 10 ? 0.3 : scanHistory.length * 0.03),
      1
    );
    
    scores.push({
      productId: product.id,
      score: Math.min(score, 1), // Cap at 1.0
      reasons,
      confidence,
    });
  }
  
  return scores;
}

/**
 * Update user preferences based on scan history
 */
export async function updateUserPreferences(userId: string): Promise<void> {
  try {
    const scanHistory = await getScanHistory(userId);
    
    if (scanHistory.length === 0) return;
    
    // Analyze categories
    const categoryCount: Record<string, number> = {};
    const brandCount: Record<string, number> = {};
    const prices: number[] = [];
    const scanTimes: Date[] = [];
    
    for (const scan of scanHistory) {
      if (scan.productId) {
        const product = await findProductByBarcode(scan.barcode);
        if (product) {
          categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;
          brandCount[product.brand] = (brandCount[product.brand] || 0) + 1;
          prices.push(product.price);
        }
      }
      scanTimes.push(new Date(scan.timestamp));
    }
    
    // Calculate preferences
    const totalScans = scanHistory.length;
    
    const favoriteCategories = Object.entries(categoryCount)
      .map(([category, count]) => ({
        category,
        score: count / totalScans,
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
    
    const favoriteBrands = Object.entries(brandCount)
      .map(([brand, count]) => ({
        brand,
        score: count / totalScans,
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 5);
    
    const priceRange = {
      min: Math.min(...prices) * 0.8,
      max: Math.max(...prices) * 1.2,
    };
    
    // Analyze scan patterns
    const hourCounts: Record<number, number> = {};
    const dayCounts: Record<string, number> = {};
    
    scanTimes.forEach(time => {
      const hour = time.getHours();
      const day = time.toLocaleDateString('en-US', { weekday: 'long' });
      
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
      dayCounts[day] = (dayCounts[day] || 0) + 1;
    });
    
    const peakHours = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour));
    
    const frequentDays = Object.entries(dayCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([day]) => day);
    
    const preferences: UserPreferences = {
      userId,
      favoriteCategories,
      favoriteBrands,
      priceRange,
      scanPatterns: {
        peakHours,
        frequentDays,
        averageSessionLength: 5, // Mock value
      },
      lastUpdated: new Date(),
    };
    
    await AsyncStorage.setItem(
      `${USER_PREFERENCES_KEY}_${userId}`,
      JSON.stringify(preferences)
    );
    
    console.log('User preferences updated successfully');
    
  } catch (error) {
    console.error('Error updating user preferences:', error);
  }
}

/**
 * Get user preferences
 */
async function getUserPreferences(userId: string): Promise<UserPreferences> {
  try {
    const stored = await AsyncStorage.getItem(`${USER_PREFERENCES_KEY}_${userId}`);
    
    if (stored) {
      const preferences = JSON.parse(stored);
      
      // Check if preferences are recent (update if older than 7 days)
      const lastUpdated = new Date(preferences.lastUpdated);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      if (lastUpdated < weekAgo) {
        await updateUserPreferences(userId);
        return getUserPreferences(userId); // Recursive call to get updated preferences
      }
      
      return preferences;
    }
    
    // Create default preferences
    const defaultPreferences: UserPreferences = {
      userId,
      favoriteCategories: [],
      favoriteBrands: [],
      priceRange: { min: 0, max: 1000 },
      scanPatterns: {
        peakHours: [],
        frequentDays: [],
        averageSessionLength: 5,
      },
      lastUpdated: new Date(),
    };
    
    await updateUserPreferences(userId);
    return defaultPreferences;
    
  } catch (error) {
    console.error('Error getting user preferences:', error);
    return {
      userId,
      favoriteCategories: [],
      favoriteBrands: [],
      priceRange: { min: 0, max: 1000 },
      scanPatterns: {
        peakHours: [],
        frequentDays: [],
        averageSessionLength: 5,
      },
      lastUpdated: new Date(),
    };
  }
}

/**
 * Get all products (mock implementation)
 */
async function getAllProducts(): Promise<Product[]> {
  // In a real app, this would fetch from API
  // For demo, return the mock products from barcodeService
  const mockProducts = [
    {
      id: 'p1',
      name: 'Organic Bananas',
      description: 'Fresh organic bananas, 1kg pack',
      price: 49,
      originalPrice: 99,
      discount: 50,
      category: 'fruits',
      brand: 'Organic Valley',
      weight: '1kg',
      imageUrl: '🍌',
      inStock: true,
      stockQuantity: 50,
      barcode: '1234567890123',
      priceHistory: [],
      lowestPrice: 45,
      highestPrice: 99,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
    },
    // Add more mock products here...
  ];
  
  return mockProducts;
}

/**
 * Get complementary products (mock implementation)
 */
async function getComplementaryProducts(
  targetProduct: Product,
  allProducts: Product[]
): Promise<Product[]> {
  // Mock complementary product logic
  // In a real app, this would use machine learning or association rules
  
  const complementaryMap: Record<string, string[]> = {
    'fruits': ['dairy', 'bakery'],
    'dairy': ['fruits', 'grains'],
    'bakery': ['dairy', 'oils'],
    'grains': ['oils', 'dairy'],
    'oils': ['grains', 'bakery'],
  };
  
  const complementaryCategories = complementaryMap[targetProduct.category] || [];
  
  return allProducts.filter(product => 
    complementaryCategories.includes(product.category) &&
    product.id !== targetProduct.id
  );
}

/**
 * Calculate product similarity score
 */
function calculateProductSimilarityScore(
  product1: Product,
  product2: Product,
  preferences: UserPreferences
): number {
  let score = 0;
  
  // Category match
  if (product1.category === product2.category) {
    score += 0.4;
  }
  
  // Brand match
  if (product1.brand === product2.brand) {
    score += 0.2;
  }
  
  // Price similarity
  const priceDiff = Math.abs(product1.price - product2.price);
  const priceScore = Math.max(0, 1 - (priceDiff / Math.max(product1.price, product2.price)));
  score += priceScore * 0.3;
  
  // User preference boost
  const categoryPref = preferences.favoriteCategories
    .find(cat => cat.category === product2.category);
  if (categoryPref) {
    score += categoryPref.score * 0.1;
  }
  
  return Math.min(score, 1);
}

/**
 * Determine recommendation type
 */
function determineRecommendationType(
  scoreData: RecommendationScore,
  preferences: UserPreferences,
  scanHistory: ScanHistory[]
): 'similar' | 'complementary' | 'trending' | 'personalized' | 'price_drop' {
  if (scoreData.reasons.includes('discount available')) {
    return 'price_drop';
  }
  
  if (scoreData.reasons.some(reason => reason.includes('You like'))) {
    return 'personalized';
  }
  
  if (scoreData.reasons.includes('Trending now')) {
    return 'trending';
  }
  
  return 'personalized'; // Default
}

/**
 * Cache recommendations
 */
async function cacheRecommendations(
  userId: string,
  recommendations: ProductRecommendation[]
): Promise<void> {
  try {
    const cacheData = {
      userId,
      recommendations,
      timestamp: new Date(),
    };
    
    await AsyncStorage.setItem(
      `${RECOMMENDATION_CACHE_KEY}_${userId}`,
      JSON.stringify(cacheData)
    );
  } catch (error) {
    console.error('Error caching recommendations:', error);
  }
}
