// Cron service for scheduled tasks like daily price tracking

import * as cron from 'node-cron';
import { trackDailyPrices, detectPriceDrops } from './priceTracker';

/**
 * Initialize all cron jobs
 */
export function initializeCronJobs(): void {
  console.log('Initializing cron jobs...');
  
  // Daily price tracking at midnight
  cron.schedule('0 0 * * *', async () => {
    console.log('Running daily price tracking...');
    try {
      await trackDailyPrices();
      console.log('Daily price tracking completed successfully');
    } catch (error) {
      console.error('Error in daily price tracking:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });
  
  // Price drop detection every 6 hours
  cron.schedule('0 */6 * * *', async () => {
    console.log('Running price drop detection...');
    try {
      await detectPriceDrops();
      console.log('Price drop detection completed successfully');
    } catch (error) {
      console.error('Error in price drop detection:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });
  
  // Weekly cleanup of old data (every Sunday at 2 AM)
  cron.schedule('0 2 * * 0', async () => {
    console.log('Running weekly data cleanup...');
    try {
      await cleanupOldData();
      console.log('Weekly data cleanup completed successfully');
    } catch (error) {
      console.error('Error in weekly data cleanup:', error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });
  
  console.log('Cron jobs initialized successfully');
}

/**
 * Stop all cron jobs
 */
export function stopCronJobs(): void {
  console.log('Stopping all cron jobs...');
  cron.getTasks().forEach((task) => {
    task.stop();
  });
  console.log('All cron jobs stopped');
}

/**
 * Manually trigger daily price tracking (for testing)
 */
export async function triggerDailyPriceTracking(): Promise<void> {
  console.log('Manually triggering daily price tracking...');
  try {
    await trackDailyPrices();
    console.log('Manual price tracking completed successfully');
  } catch (error) {
    console.error('Error in manual price tracking:', error);
    throw error;
  }
}

/**
 * Manually trigger price drop detection (for testing)
 */
export async function triggerPriceDropDetection(): Promise<void> {
  console.log('Manually triggering price drop detection...');
  try {
    await detectPriceDrops();
    console.log('Manual price drop detection completed successfully');
  } catch (error) {
    console.error('Error in manual price drop detection:', error);
    throw error;
  }
}

/**
 * Clean up old data to prevent storage bloat
 */
async function cleanupOldData(): Promise<void> {
  try {
    // This would typically clean up:
    // - Old price history data (keep only last 6 months)
    // - Old scan history (keep only last 3 months)
    // - Old notifications (keep only last 1 month)
    // - Expired price alerts
    
    console.log('Cleaning up old data...');
    
    // For now, this is a placeholder
    // In a real implementation, you would:
    // 1. Clean old price history entries
    // 2. Clean old scan history entries
    // 3. Clean old notifications
    // 4. Clean expired alerts
    
    console.log('Data cleanup completed');
    
  } catch (error) {
    console.error('Error during data cleanup:', error);
    throw error;
  }
}

/**
 * Get cron job status
 */
export function getCronJobStatus(): {
  totalJobs: number;
  runningJobs: number;
  jobs: Array<{
    name: string;
    running: boolean;
    nextRun?: Date;
  }>;
} {
  const tasks = cron.getTasks();
  const jobs = Array.from(tasks.entries()).map(([name, task]) => ({
    name,
    running: task.getStatus() === 'scheduled',
    // Note: node-cron doesn't provide next run time directly
    // In a production app, you might want to use a more advanced scheduler
  }));
  
  return {
    totalJobs: tasks.size,
    runningJobs: jobs.filter(job => job.running).length,
    jobs,
  };
}

/**
 * Schedule a one-time price check for a specific product
 */
export function scheduleProductPriceCheck(productId: string, delayMinutes: number = 5): void {
  const scheduleTime = new Date(Date.now() + delayMinutes * 60 * 1000);
  const cronExpression = `${scheduleTime.getMinutes()} ${scheduleTime.getHours()} ${scheduleTime.getDate()} ${scheduleTime.getMonth() + 1} *`;
  
  cron.schedule(cronExpression, async () => {
    console.log(`Running scheduled price check for product: ${productId}`);
    try {
      // This would check the price for a specific product
      // await checkProductPrice(productId);
      console.log(`Price check completed for product: ${productId}`);
    } catch (error) {
      console.error(`Error checking price for product ${productId}:`, error);
    }
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });
  
  console.log(`Scheduled price check for product ${productId} at ${scheduleTime.toISOString()}`);
}

/**
 * Development helper: Run all jobs immediately for testing
 */
export async function runAllJobsNow(): Promise<void> {
  console.log('Running all cron jobs immediately for testing...');
  
  try {
    console.log('1. Running daily price tracking...');
    await trackDailyPrices();
    
    console.log('2. Running price drop detection...');
    await detectPriceDrops();
    
    console.log('3. Running data cleanup...');
    await cleanupOldData();
    
    console.log('All jobs completed successfully');
    
  } catch (error) {
    console.error('Error running jobs:', error);
    throw error;
  }
}
