import Colors from '@/constants/Colors';
import { FontAwesome } from '@expo/vector-icons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import React, { useCallback, useState } from 'react';
import {
    Alert,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { getWatchedProducts, removeFromWatchlist } from '@/services/priceTracker';
import { RootStackScreenProps } from '@/types/navigation';
import { Product } from '@/types/product';

// Mock current user ID - in a real app, this would come from auth context
const CURRENT_USER_ID = 'user_1';

type WatchedProduct = Product & { watchedAt: Date };

export default function WatchlistScreen() {
  const navigation = useNavigation<RootStackScreenProps<'watchlist'>['navigation']>();
  const [watchedProducts, setWatchedProducts] = useState<WatchedProduct[]>([]);
  const [loading, setLoading] = useState(true);

  const loadWatchedProducts = useCallback(async () => {
    try {
      setLoading(true);
      const products = await getWatchedProducts(CURRENT_USER_ID);
      setWatchedProducts(products);
    } catch (error) {
      console.error('Error loading watched products:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadWatchedProducts();
    }, [loadWatchedProducts])
  );

  const handleRemoveFromWatchlist = useCallback(async (productId: string, productName: string) => {
    Alert.alert(
      'Remove from Watchlist',
      `Stop watching price changes for ${productName}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeFromWatchlist(CURRENT_USER_ID, productId);
              setWatchedProducts(prev => prev.filter(p => p.id !== productId));
            } catch (error) {
              console.error('Error removing from watchlist:', error);
              Alert.alert('Error', 'Failed to remove product from watchlist');
            }
          },
        },
      ]
    );
  }, []);

  const handleAddToCart = useCallback((productId: string, productName: string) => {
    // In a real app, this would add the product to cart
    console.log('Adding to cart:', productId);
    Alert.alert('Added to Cart', `${productName} has been added to your cart`);
  }, []);

  const handleProductPress = useCallback((productId: string) => {
    navigation.navigate('product-details', { productId });
  }, [navigation]);

  const calculateSavings = useCallback((product: Product) => {
    if (!product.highestPrice || product.price >= product.highestPrice) {
      return null;
    }
    
    const savings = product.highestPrice - product.price;
    const percentage = Math.round((savings / product.highestPrice) * 100);
    
    return { amount: savings, percentage };
  }, []);

  const renderWatchedItem = useCallback(({ item }: { item: WatchedProduct }) => {
    const savings = calculateSavings(item);
    
    return (
      <TouchableOpacity 
        style={styles.watchedItem}
        onPress={() => handleProductPress(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.productImageContainer}>
          <Text style={styles.productImage}>{item.imageUrl || '📦'}</Text>
          {savings && (
            <View style={styles.savingsBadge}>
              <Text style={styles.savingsText}>-{savings.percentage}%</Text>
            </View>
          )}
        </View>
        
        <View style={styles.productInfo}>
          <Text style={styles.productName} numberOfLines={2}>
            {item.name}
          </Text>
          <Text style={styles.productBrand}>{item.brand}</Text>
          
          <View style={styles.priceContainer}>
            <Text style={styles.currentPrice}>₹{item.price}</Text>
            {item.lowestPrice && item.lowestPrice < item.price && (
              <Text style={styles.lowestPrice}>
                Lowest: ₹{item.lowestPrice}
              </Text>
            )}
          </View>
          
          {savings && (
            <View style={styles.savingsContainer}>
              <FontAwesome name="arrow-down" size={12} color={Colors.accent.success} />
              <Text style={styles.savingsAmount}>
                Save ₹{savings.amount} from highest price
              </Text>
            </View>
          )}
          
          <Text style={styles.watchedDate}>
            Watching since {new Date(item.watchedAt).toLocaleDateString()}
          </Text>
        </View>
        
        <View style={styles.actionButtons}>
          <TouchableOpacity 
            style={styles.addToCartButton}
            onPress={() => handleAddToCart(item.id, item.name)}
          >
            <FontAwesome name="shopping-cart" size={16} color="#FFFFFF" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.removeButton}
            onPress={() => handleRemoveFromWatchlist(item.id, item.name)}
          >
            <FontAwesome name="times" size={16} color={Colors.subtleText} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  }, [calculateSavings, handleProductPress, handleAddToCart, handleRemoveFromWatchlist]);

  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyContainer}>
      <FontAwesome name="bell-slash" size={64} color={Colors.subtleText} />
      <Text style={styles.emptyTitle}>No Price Alerts</Text>
      <Text style={styles.emptyText}>
        Add products to your watchlist to get notified when prices drop
      </Text>
      <TouchableOpacity 
        style={styles.browseButton}
        onPress={() => navigation.navigate('(tabs)', { screen: 'search' })}
      >
        <Text style={styles.browseButtonText}>Browse Products</Text>
      </TouchableOpacity>
    </View>
  ), [navigation]);

  const renderHeader = useCallback(() => (
    <View style={styles.header}>
      <TouchableOpacity 
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <FontAwesome name="arrow-left" size={20} color={Colors.text} />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Price Alerts</Text>
      <View style={styles.headerRight}>
        {watchedProducts.length > 0 && (
          <Text style={styles.watchedCount}>
            {watchedProducts.length} watching
          </Text>
        )}
      </View>
    </View>
  ), [navigation, watchedProducts.length]);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your watchlist...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      {watchedProducts.length > 0 && (
        <View style={styles.infoCard}>
          <FontAwesome name="info-circle" size={16} color={Colors.primary.start} />
          <Text style={styles.infoText}>
            You'll be notified when prices drop for these products
          </Text>
        </View>
      )}
      
      <FlatList
        data={watchedProducts}
        keyExtractor={(item) => item.id}
        renderItem={renderWatchedItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    minWidth: 40,
    alignItems: 'flex-end',
  },
  watchedCount: {
    fontSize: 12,
    color: Colors.subtleText,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(94, 23, 235, 0.1)',
    margin: 16,
    padding: 12,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 14,
    color: Colors.text,
    marginLeft: 8,
    flex: 1,
  },
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
  },
  watchedItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  productImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    position: 'relative',
  },
  productImage: {
    fontSize: 24,
  },
  savingsBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.accent.success,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  savingsText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  productBrand: {
    fontSize: 12,
    color: Colors.subtleText,
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  currentPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginRight: 8,
  },
  lowestPrice: {
    fontSize: 12,
    color: Colors.accent.success,
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  savingsAmount: {
    fontSize: 12,
    color: Colors.accent.success,
    marginLeft: 4,
  },
  watchedDate: {
    fontSize: 11,
    color: Colors.subtleText,
  },
  actionButtons: {
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: 8,
  },
  addToCartButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.primary.start,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  removeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.subtleText,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  browseButton: {
    backgroundColor: Colors.primary.start,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.subtleText,
  },
});
