import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from 'expo-router';
import React, { memo, useCallback, useRef, useState } from 'react';
import { Animated, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import CategoryGrid, { Category } from '@/components/CategoryGrid';
import Header from '@/components/Header';
import SearchBar from '@/components/SearchBar';
import SpecialOffers, { Offer } from '@/components/SpecialOffers';
import ShadowCard from '@/components/ui/ShadowCard';
import Colors from '@/constants/Colors';
import { TabScreenProps } from '@/types/navigation';

// Check if running on web
const IS_WEB = Platform.OS === 'web';

// Flash deals data
const FLASH_DEALS = [
  {
    id: 'fd1',
    name: 'Organic Bananas',
    originalPrice: 99,
    discountPrice: 49,
    image: '🍌', // Using emoji for demo, in production use actual images
    timeLeft: '1h 23m',
    percentOff: 50,
  },
  {
    id: 'fd2',
    name: 'Fresh Milk 1L',
    originalPrice: 89,
    discountPrice: 59,
    image: '🥛',
    timeLeft: '2h 15m',
    percentOff: 33,
  },
  {
    id: 'fd3',
    name: 'Whole Wheat Bread',
    originalPrice: 45,
    discountPrice: 35,
    image: '🍞',
    timeLeft: '45m',
    percentOff: 22,
  },
  {
    id: 'fd4',
    name: 'Tomatoes 1kg',
    originalPrice: 80,
    discountPrice: 49,
    image: '🍅',
    timeLeft: '3h 10m',
    percentOff: 40,
  },
];

// Membership benefits
const MEMBERSHIP_BENEFITS = [
  { id: 'mb1', icon: 'truck' as const, title: 'Free Delivery', description: 'All orders, no minimum' },
  { id: 'mb2', icon: 'bolt' as const, title: 'Priority Delivery', description: 'Within 10 mins' },
  { id: 'mb3', icon: 'gift' as const, title: 'Exclusive Offers', description: 'Member-only deals' },
  { id: 'mb4', icon: 'tags' as const, title: 'Extra Discounts', description: '5% off everything' },
];

// Memoized FlashDealItem component
const FlashDealItem = memo(({ item }: { item: typeof FLASH_DEALS[0] }) => {
  return (
    <ShadowCard depth="low" style={styles.flashDealCard} borderRadius={12}>
      <View style={styles.flashDealContent}>
        <View style={styles.flashDealImageContainer}>
          <Text style={styles.flashDealImage}>{item.image}</Text>
          <View style={styles.discountBadge}>
            <Text style={styles.discountText}>-{item.percentOff}%</Text>
          </View>
        </View>
        <View style={styles.flashDealInfo}>
          <Text style={styles.flashDealName} numberOfLines={1}>{item.name}</Text>
          <View style={styles.flashDealPriceRow}>
            <Text style={styles.discountPrice}>₹{item.discountPrice}</Text>
            <Text style={styles.originalPrice}>₹{item.originalPrice}</Text>
          </View>
          <View style={styles.flashDealTimeRow}>
            <FontAwesome name="clock-o" size={12} color={Colors.accent.warning} />
            <Text style={styles.timeLeftText}>{item.timeLeft} left</Text>
          </View>
        </View>
      </View>
    </ShadowCard>
  );
});

// Memoized Membership benefit component
const MembershipBenefit = memo(({ item }: { item: typeof MEMBERSHIP_BENEFITS[0] }) => {
  return (
    <View style={styles.membershipBenefit}>
      <View style={styles.membershipIconContainer}>
        <FontAwesome name={item.icon} size={16} color="#FFF" />
      </View>
      <View style={styles.membershipTextContainer}>
        <Text style={styles.membershipTitle}>{item.title}</Text>
        <Text style={styles.membershipDescription}>{item.description}</Text>
      </View>
    </View>
  );
});

const HomeScreen = () => {
  const navigation = useNavigation<TabScreenProps<'index'>['navigation']>();
  const [searchFocused, setSearchFocused] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;

  const handleCategoryPress = useCallback((category: Category) => {
    navigation.navigate('category-products', {
      categoryId: category.id,
      categoryName: category.name,
    });
  }, [navigation]);

  const handleOfferPress = useCallback((offer: Offer) => {
    // Navigate to offer details or apply offer directly
    if (offer.title.includes('OFF')) {
      navigation.navigate('search'); // Navigate to search with applied filter
    } else if (offer.title.includes('Delivery')) {
      // Show delivery options or pass to checkout
      console.log('Offer applied:', offer.title);
    } else {
      // Generic offer handling
      console.log('Offer pressed:', offer.title);
    }
  }, [navigation]);

  const handleSearchPress = useCallback(() => {
    // Animate search before navigating
    setSearchFocused(true);
    const timeout = setTimeout(() => {
      navigation.navigate('search');
      // Reset search focus when coming back
      const resetTimeout = setTimeout(() => setSearchFocused(false), 500);
      return () => clearTimeout(resetTimeout);
    }, 300);
    
    return () => clearTimeout(timeout);
  }, [navigation]);

  const handleJoinMembership = useCallback(() => {
    // Navigate to membership subscription page
    console.log('Navigate to membership page');
  }, []);

  const handleViewAllFlashDeals = useCallback(() => {
    // Navigate to full flash deals page
    console.log('Navigate to all flash deals');
  }, []);

  // Memoized scroll event handler for better performance
  const handleScroll = useCallback(
    Animated.event(
      [{ nativeEvent: { contentOffset: { y: scrollY } } }],
      { useNativeDriver: true } // Use native driver for scroll animations
    ),
    [scrollY]
  );

  // Header shrink effect based on scroll
  const headerScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.95],
    extrapolate: 'clamp',
  });

  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -10],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 60, 100],
    outputRange: [1, 1, 0.95],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.start} />
      
      <Animated.View 
        style={[
          styles.headerContainer, 
          { 
            transform: [
              { scale: headerScale },
              { translateY: headerTranslateY }
            ],
            opacity: headerOpacity
          }
        ]}
      >
        <Header location="Home • Sector 62, Noida" />
      </Animated.View>
      
      <SearchBar onPress={handleSearchPress} focused={searchFocused} />
      
      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        removeClippedSubviews={true}
      >
        <CategoryGrid onCategoryPress={handleCategoryPress} />
        
        {/* Flash Deals Section */}
        <View style={styles.flashDealsSection}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <FontAwesome name="bolt" size={18} color={Colors.accent.warning} style={styles.sectionIcon} />
              <Text style={styles.sectionTitle}>Flash Deals</Text>
            </View>
            <TouchableOpacity onPress={handleViewAllFlashDeals}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {/* Timer banner */}
          <View style={styles.timerBanner}>
            <Text style={styles.timerText}>Ends in: </Text>
            <View style={styles.timerBox}>
              <Text style={styles.timerDigit}>03</Text>
            </View>
            <Text style={styles.timerSeparator}>:</Text>
            <View style={styles.timerBox}>
              <Text style={styles.timerDigit}>45</Text>
            </View>
            <Text style={styles.timerSeparator}>:</Text>
            <View style={styles.timerBox}>
              <Text style={styles.timerDigit}>22</Text>
            </View>
          </View>
          
          {/* Flash deals scrollview */}
          <Animated.ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.flashDealsContainer}
          >
            {FLASH_DEALS.map(item => (
              <FlashDealItem key={item.id} item={item} />
            ))}
          </Animated.ScrollView>
        </View>
        
        <SpecialOffers onOfferPress={handleOfferPress} />
        
        {/* Membership Benefits Section */}
        <View style={styles.membershipSection}>
          <ShadowCard depth="medium" style={styles.membershipCard} borderRadius={16}>
            <LinearGradient
              colors={[Colors.primary.start, Colors.primary.end]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.membershipGradient}
            >
              <View style={styles.membershipHeader}>
                <View>
                  <Text style={styles.membershipCardTitle}>Premium Membership</Text>
                  <Text style={styles.membershipCardSubtitle}>Unlock exclusive benefits</Text>
                </View>
                <View style={styles.membershipPriceTag}>
                  <Text style={styles.membershipPrice}>₹99</Text>
                  <Text style={styles.membershipPeriod}>/month</Text>
                </View>
              </View>
              
              <View style={styles.membershipBenefitsContainer}>
                {MEMBERSHIP_BENEFITS.map(benefit => (
                  <MembershipBenefit key={benefit.id} item={benefit} />
                ))}
              </View>
              
              <TouchableOpacity 
                style={styles.joinButton}
                onPress={handleJoinMembership}
                activeOpacity={0.8}
              >
                <Text style={styles.joinButtonText}>Join Now</Text>
              </TouchableOpacity>
            </LinearGradient>
          </ShadowCard>
        </View>
        
        {/* Add some spacing at the bottom for better UX */}
        <View style={styles.bottomSpacing} />
      </Animated.ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerContainer: {
    zIndex: 10,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    overflow: 'hidden',
  },
  scrollContent: {
    flexGrow: 1,
  },
  bottomSpacing: {
    height: 80,
  },
  flashDealsSection: {
    marginVertical: 10,
    paddingVertical: 15,
    backgroundColor: Colors.background,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
  },
  viewAllText: {
    color: Colors.primary.start,
    fontWeight: '600',
    fontSize: 14,
  },
  timerBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 15,
  },
  timerText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginRight: 8,
  },
  timerBox: {
    backgroundColor: Colors.accent.error,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 4,
  },
  timerDigit: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  timerSeparator: {
    color: Colors.accent.error,
    fontWeight: 'bold',
    fontSize: 14,
    marginHorizontal: 2,
  },
  flashDealsContainer: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  flashDealCard: {
    width: 160,
    height: 200,
    marginRight: 12,
    overflow: 'hidden',
    borderRadius: 12,
  },
  flashDealContent: {
    padding: 8,
    height: '100%',
  },
  flashDealImageContainer: {
    position: 'relative',
    height: 100,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  flashDealImage: {
    fontSize: 40,
  },
  discountBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: Colors.accent.error,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderTopRightRadius: 8,
    borderBottomLeftRadius: 8,
  },
  discountText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 10,
  },
  flashDealInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  flashDealName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  flashDealPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  discountPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.accent.error,
    marginRight: 6,
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
    color: Colors.subtleText,
  },
  flashDealTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeLeftText: {
    fontSize: 12,
    color: Colors.accent.warning,
    marginLeft: 4,
    fontWeight: '500',
  },
  membershipSection: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  membershipCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  membershipGradient: {
    padding: 20,
    borderRadius: 16,
  },
  membershipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  membershipCardTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  membershipCardSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  membershipPriceTag: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  membershipPrice: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  membershipPeriod: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 3,
  },
  membershipBenefitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  membershipBenefit: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 12,
  },
  membershipIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  membershipTextContainer: {
    flex: 1,
  },
  membershipTitle: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  membershipDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  joinButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinButtonText: {
    color: Colors.primary.start,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default memo(HomeScreen);
