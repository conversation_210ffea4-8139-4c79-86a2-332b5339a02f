import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import Colors from '@/constants/Colors';

interface Order {
  id: string;
  orderNumber: string;
  date: string;
  items: number;
  amount: number;
  status: 'delivered' | 'processing' | 'cancelled';
}

// Sample orders data
const orders: Order[] = [
  {
    id: '1',
    orderNumber: 'ZEP-2023-123456',
    date: '12 May, 2023 • 10:30 AM',
    items: 3,
    amount: 148,
    status: 'delivered',
  },
  {
    id: '2',
    orderNumber: 'ZEP-2023-123457',
    date: '10 May, 2023 • 09:45 AM',
    items: 5,
    amount: 273,
    status: 'delivered',
  },
  {
    id: '3',
    orderNumber: 'ZEP-2023-123458',
    date: '08 May, 2023 • 02:15 PM',
    items: 1,
    amount: 59,
    status: 'cancelled',
  },
  {
    id: '4',
    orderNumber: 'ZEP-2023-123459',
    date: 'Today • 11:20 AM',
    items: 4,
    amount: 210,
    status: 'processing',
  },
];

export default function OrdersScreen() {
  const getStatusColor = (status: Order['status']): string => {
    switch (status) {
      case 'delivered':
        return '#4CAF50';
      case 'processing':
        return '#FFA000';
      case 'cancelled':
        return '#F44336';
      default:
        return Colors.subtleText;
    }
  };

  const getStatusIcon = (status: Order['status']): keyof typeof FontAwesome.glyphMap => {
    switch (status) {
      case 'delivered':
        return 'check-circle';
      case 'processing':
        return 'clock-o';
      case 'cancelled':
        return 'times-circle';
      default:
        return 'question-circle';
    }
  };

  const renderItem = ({ item }: { item: Order }) => (
    <TouchableOpacity style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>{item.orderNumber}</Text>
        <View style={styles.statusContainer}>
          <FontAwesome 
            name={getStatusIcon(item.status)} 
            size={14} 
            color={getStatusColor(item.status)}
            style={styles.statusIcon}
          />
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
      
      <Text style={styles.orderDate}>{item.date}</Text>
      
      <View style={styles.orderFooter}>
        <Text style={styles.itemCount}>{item.items} {item.items > 1 ? 'items' : 'item'}</Text>
        <Text style={styles.orderAmount}>₹{item.amount}</Text>
      </View>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.actionButton}>
          <FontAwesome name="repeat" size={14} color={Colors.primary.end} />
          <Text style={styles.actionText}>Reorder</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <FontAwesome name="file-text-o" size={14} color={Colors.text} />
          <Text style={styles.actionText}>Details</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <LinearGradient
        colors={[Colors.primary.start, Colors.primary.end]}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>My Orders</Text>
      </LinearGradient>

      <FlatList
        data={orders}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.ordersList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  ordersList: {
    padding: 16,
  },
  orderCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  orderDate: {
    fontSize: 14,
    color: Colors.subtleText,
    marginBottom: 12,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemCount: {
    fontSize: 14,
    color: Colors.subtleText,
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
  },
  actionButtons: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  actionText: {
    fontSize: 14,
    marginLeft: 6,
    color: Colors.text,
  },
}); 