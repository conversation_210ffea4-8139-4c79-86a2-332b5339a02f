import { Colors } from '@/constants/Colors';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { FlatList, ScrollView, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

// Types
interface BudgetCategory {
  id: string;
  name: string;
  icon: keyof typeof FontAwesome.glyphMap;
  defaultPercent: number;
  percent: number;
  amount: number;
}

// Sample data
const BUDGET_CATEGORIES = [
  { id: 'essentials', name: 'Essentials', icon: 'shopping-cart' as keyof typeof FontAwesome.glyphMap, defaultPercent: 40 },
  { id: 'fruits', name: 'Fruits & Vegetables', icon: 'leaf' as keyof typeof FontAwesome.glyphMap, defaultPercent: 25 },
  { id: 'dairy', name: 'Dairy & Eggs', icon: 'glass' as keyof typeof FontAwesome.glyphMap, defaultPercent: 15 },
  { id: 'snacks', name: 'Snacks', icon: 'cutlery' as keyof typeof FontAwesome.glyphMap, defaultPercent: 10 },
  { id: 'beverages', name: 'Beverages', icon: 'coffee' as keyof typeof FontAwesome.glyphMap, defaultPercent: 10 },
];

export default function BudgetShoppingScreen() {
  const [totalBudget, setTotalBudget] = useState('1000');
  const [categories, setCategories] = useState<BudgetCategory[]>(BUDGET_CATEGORIES.map(cat => ({
    ...cat,
    percent: cat.defaultPercent,
    amount: Math.round((cat.defaultPercent / 100) * 1000)
  })));

  const updateCategoryPercent = (id: string, newPercent: number) => {
    const updatedCategories = categories.map(cat => {
      if (cat.id === id) {
        return {
          ...cat,
          percent: newPercent,
          amount: Math.round((newPercent / 100) * parseInt(totalBudget))
        };
      }
      return cat;
    });
    setCategories(updatedCategories);
  };

  const updateTotalBudget = (value: string) => {
    const numValue = parseInt(value) || 0;
    setTotalBudget(value);

    const updatedCategories = categories.map(cat => ({
      ...cat,
      amount: Math.round((cat.percent / 100) * numValue)
    }));
    setCategories(updatedCategories);
  };
  
  const getTotalAllocated = () => {
    return categories.reduce((sum, cat) => sum + cat.amount, 0);
  };
  
  const startBudgetShopping = () => {
    // In a real app, this would navigate to a filtered product list
    console.log('Starting budget shopping with categories:', categories);
  };

  const renderCategoryItem = ({ item }: { item: BudgetCategory }) => (
    <View style={styles.categoryItem}>
      <View style={styles.categoryHeader}>
        <FontAwesome name={item.icon} size={20} color={Colors.primary.start} />
        <Text style={styles.categoryName}>{item.name}</Text>
      </View>
      <View style={styles.categoryDetails}>
        <Text style={styles.categoryPercent}>{item.percent}%</Text>
        <Text style={styles.categoryAmount}>₹{item.amount}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[Colors.primary.start, Colors.primary.end]}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Budget Shopping</Text>
        <Text style={styles.headerSubtitle}>Shop smarter within your budget</Text>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.budgetInputContainer}>
          <Text style={styles.budgetLabel}>Your Total Budget</Text>
          <View style={styles.budgetInputWrapper}>
            <Text style={styles.currencySymbol}>₹</Text>
            <TextInput
              style={styles.budgetInput}
              value={totalBudget}
              onChangeText={updateTotalBudget}
              keyboardType="numeric"
              maxLength={6}
            />
          </View>
        </View>

        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Budget Allocation</Text>
          <Text style={styles.summaryText}>
            Total Allocated: ₹{getTotalAllocated()} / ₹{totalBudget}
          </Text>
        </View>

        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          style={styles.categoriesList}
        />

        <TouchableOpacity style={styles.startButton} onPress={startBudgetShopping}>
          <Text style={styles.startButtonText}>Start Budget Shopping</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  budgetInputContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  budgetLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 10,
  },
  budgetInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderWidth: 2,
    borderColor: Colors.border,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary.start,
    marginRight: 5,
  },
  budgetInput: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    minWidth: 100,
    textAlign: 'center',
  },
  summaryContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 5,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.subtleText,
  },
  categoriesList: {
    marginBottom: 20,
  },
  categoryItem: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginLeft: 10,
  },
  categoryDetails: {
    alignItems: 'flex-end',
  },
  categoryPercent: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.start,
  },
  categoryAmount: {
    fontSize: 14,
    color: Colors.subtleText,
    marginTop: 2,
  },
  startButton: {
    backgroundColor: Colors.primary.start,
    borderRadius: 12,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 30,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});
