import PriceAlertSettings, { PriceAlertSettings as AlertSettings } from '@/components/PriceAlertSettings';
import PriceHistoryChart from '@/components/PriceHistoryChart';
import ShadowCard from '@/components/ui/ShadowCard';
import Colors from '@/constants/Colors';
import { addToWatchlist, isProductWatched, removeFromWatchlist } from '@/services/priceTracker';
import { Product } from '@/types/product';
import { FontAwesome } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    StatusBar,
    Text,
    TouchableOpacity,
    View
} from 'react-native';


// Mock user ID - in a real app, this would come from authentication
const MOCK_USER_ID = 'user_123';

// Mock product data - in a real app, this would be fetched from API
const MOCK_PRODUCT: Product = {
  id: 'p1',
  name: 'Organic Bananas',
  description: 'Fresh organic bananas, 1kg pack. Rich in potassium and natural sugars, perfect for a healthy snack or smoothie ingredient.',
  price: 49,
  originalPrice: 99,
  discount: 50,
  category: 'fruits',
  brand: 'Organic Valley',
  weight: '1kg',
  imageUrl: '🍌',
  inStock: true,
  stockQuantity: 50,
  barcode: '1234567890123',
  priceHistory: [
    { price: 99, date: new Date('2024-01-01') },
    { price: 89, date: new Date('2024-01-08') },
    { price: 79, date: new Date('2024-01-15') },
    { price: 69, date: new Date('2024-01-22') },
    { price: 59, date: new Date('2024-02-01') },
    { price: 55, date: new Date('2024-02-08') },
    { price: 49, date: new Date('2024-02-15') },
  ],
  lowestPrice: 45,
  highestPrice: 99,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date(),
};

export default function ProductDetailsScreen() {
  const { productId, productName } = useLocalSearchParams<{ productId: string; productName: string }>();
  const navigation = useNavigation();
  const [product, setProduct] = useState<Product | null>(null);
  const [isWatched, setIsWatched] = useState(false);
  const [loading, setLoading] = useState(true);
  const [watchLoading, setWatchLoading] = useState(false);
  const [showAlertSettings, setShowAlertSettings] = useState(false);

  const loadProduct = useCallback(async () => {
    try {
      // In a real app, fetch product by ID from API
      setProduct(MOCK_PRODUCT);

      // Check if product is being watched
      const watched = await isProductWatched(MOCK_USER_ID, productId || 'p1');
      setIsWatched(watched);
    } catch (error) {
      console.error('Error loading product:', error);
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    loadProduct();
  }, [loadProduct]);

  const handleWatchToggle = useCallback(async () => {
    if (!product) return;

    setWatchLoading(true);
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      if (isWatched) {
        await removeFromWatchlist(MOCK_USER_ID, product.id);
        setIsWatched(false);
        Alert.alert('Removed from Watchlist', `${product.name} has been removed from your price alerts.`);
      } else {
        await addToWatchlist(MOCK_USER_ID, product.id, {
          notifyOnAnyDrop: true,
          dropThreshold: 5,
        });
        setIsWatched(true);
        Alert.alert('Added to Watchlist', `You'll be notified when ${product.name} price drops.`);
      }
    } catch (error) {
      console.error('Error toggling watch status:', error);
      Alert.alert('Error', 'Failed to update watchlist. Please try again.');
    } finally {
      setWatchLoading(false);
    }
  }, [product, isWatched]);

  const handleAlertSettingsPress = useCallback(() => {
    setShowAlertSettings(true);
  }, []);

  const handleAlertSettingsSave = useCallback(async (settings: AlertSettings) => {
    if (!product) return;

    try {
      // Update watchlist with new settings
      await addToWatchlist(MOCK_USER_ID, product.id, settings);
      Alert.alert('Settings Updated', 'Your price alert settings have been updated.');
    } catch (error) {
      console.error('Error updating alert settings:', error);
      Alert.alert('Error', 'Failed to update alert settings. Please try again.');
    }
  }, [product]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.start} />
        <Text style={styles.loadingText}>Loading product details...</Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome name="exclamation-triangle" size={64} color={Colors.subtleText} />
        <Text style={styles.errorTitle}>Product Not Found</Text>
        <Text style={styles.errorText}>The requested product could not be found.</Text>
        <TouchableOpacity style={styles.backToHomeButton} onPress={() => navigation.goBack()}>
          <Text style={styles.backToHomeButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.start} />

      <LinearGradient
        colors={[Colors.primary.start, Colors.primary.end]}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesome name="arrow-left" size={20} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>{product.name}</Text>
        <TouchableOpacity
          style={[styles.watchButton, isWatched && styles.watchButtonActive]}
          onPress={handleWatchToggle}
          disabled={watchLoading}
        >
          {watchLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <FontAwesome
              name={isWatched ? "bell" : "bell-o"}
              size={20}
              color="#FFFFFF"
            />
          )}
        </TouchableOpacity>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Info Card */}
        <ShadowCard style={styles.productCard}>
          <View style={styles.productHeader}>
            <Text style={styles.productEmoji}>{product.imageUrl}</Text>
            <View style={styles.productInfo}>
              <Text style={styles.productName}>{product.name}</Text>
              <Text style={styles.productBrand}>{product.brand}</Text>
              <Text style={styles.productWeight}>{product.weight}</Text>
            </View>
          </View>

          <View style={styles.priceSection}>
            <View style={styles.priceRow}>
              <Text style={styles.currentPrice}>₹{product.price}</Text>
              {product.originalPrice && product.originalPrice > product.price && (
                <>
                  <Text style={styles.originalPrice}>₹{product.originalPrice}</Text>
                  <View style={styles.discountBadge}>
                    <Text style={styles.discountText}>{product.discount}% OFF</Text>
                  </View>
                </>
              )}
            </View>
            <Text style={styles.savings}>
              You save ₹{(product.originalPrice || 0) - product.price}
            </Text>
          </View>

          <View style={styles.stockSection}>
            <FontAwesome
              name={product.inStock ? "check-circle" : "times-circle"}
              size={16}
              color={product.inStock ? Colors.accent.success : Colors.accent.error}
            />
            <Text style={[
              styles.stockText,
              { color: product.inStock ? Colors.accent.success : Colors.accent.error }
            ]}>
              {product.inStock ? `In Stock (${product.stockQuantity} available)` : 'Out of Stock'}
            </Text>
          </View>

          <Text style={styles.description}>{product.description}</Text>
        </ShadowCard>

        {/* Price History Chart */}
        {product.priceHistory && product.priceHistory.length > 1 && (
          <ShadowCard style={styles.chartCard}>
            <Text style={styles.chartTitle}>Price History</Text>
            <PriceHistoryChart data={product.priceHistory} />
          </ShadowCard>
        )}

        {/* Watch Settings Card */}
        {isWatched && (
          <ShadowCard style={styles.watchCard}>
            <View style={styles.watchHeader}>
              <FontAwesome name="bell" size={20} color={Colors.primary.start} />
              <Text style={styles.watchTitle}>Price Alert Active</Text>
            </View>
            <Text style={styles.watchDescription}>
              You'll be notified when this product's price drops by 5% or more.
            </Text>
            <TouchableOpacity style={styles.watchSettingsButton} onPress={handleAlertSettingsPress}>
              <Text style={styles.watchSettingsText}>Customize Alert Settings</Text>
              <FontAwesome name="chevron-right" size={14} color={Colors.primary.start} />
            </TouchableOpacity>
          </ShadowCard>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.addToCartButton}>
            <LinearGradient
              colors={[Colors.primary.start, Colors.primary.end]}
              style={styles.addToCartGradient}
            >
              <FontAwesome name="shopping-cart" size={20} color="#FFFFFF" />
              <Text style={styles.addToCartText}>Add to Cart</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity style={styles.shareButton}>
            <FontAwesome name="share" size={20} color={Colors.primary.start} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Price Alert Settings Modal */}
      <PriceAlertSettings
        visible={showAlertSettings}
        onClose={() => setShowAlertSettings(false)}
        onSave={handleAlertSettingsSave}
        productName={product.name}
        currentPrice={product.price}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.subtleText,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 20,
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    color: Colors.subtleText,
    textAlign: 'center',
    marginBottom: 24,
  },
  backToHomeButton: {
    backgroundColor: Colors.primary.start,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
  },
  backToHomeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  watchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  watchButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  productCard: {
    padding: 20,
    marginBottom: 16,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  productEmoji: {
    fontSize: 48,
    marginRight: 16,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 16,
    color: Colors.subtleText,
    marginBottom: 2,
  },
  productWeight: {
    fontSize: 14,
    color: Colors.subtleText,
  },
  priceSection: {
    marginBottom: 16,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary.start,
    marginRight: 12,
  },
  originalPrice: {
    fontSize: 18,
    color: Colors.subtleText,
    textDecorationLine: 'line-through',
    marginRight: 12,
  },
  discountBadge: {
    backgroundColor: Colors.accent.success,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  savings: {
    fontSize: 14,
    color: Colors.accent.success,
    fontWeight: '500',
  },
  stockSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stockText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  chartCard: {
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  watchCard: {
    padding: 20,
    marginBottom: 16,
    backgroundColor: Colors.card,
  },
  watchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  watchTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginLeft: 12,
  },
  watchDescription: {
    fontSize: 14,
    color: Colors.subtleText,
    lineHeight: 20,
    marginBottom: 16,
  },
  watchSettingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  watchSettingsText: {
    fontSize: 14,
    color: Colors.primary.start,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  addToCartButton: {
    flex: 1,
    marginRight: 12,
  },
  addToCartGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 25,
  },
  addToCartText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  shareButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
  },
});