import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from 'expo-router';
import Colors from '@/constants/Colors';
import ShadowCard from '@/components/ui/ShadowCard';
import { ScanHistory, Product } from '@/types/product';
import { 
  getScanHistory, 
  clearScanHistory, 
  getScanStatistics,
  findProductByBarcode 
} from '@/services/barcodeService';

// Mock user ID - in a real app, this would come from authentication
const MOCK_USER_ID = 'user_123';

export default function ScanHistoryScreen() {
  const navigation = useNavigation();
  const [scanHistory, setScanHistory] = useState<ScanHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [statistics, setStatistics] = useState({
    totalScans: 0,
    uniqueProducts: 0,
    todayScans: 0,
    weekScans: 0,
  });

  const loadScanHistory = useCallback(async () => {
    try {
      const history = await getScanHistory(MOCK_USER_ID);
      setScanHistory(history);
      
      const stats = await getScanStatistics(MOCK_USER_ID);
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading scan history:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadScanHistory();
  }, [loadScanHistory]);

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadScanHistory();
  }, [loadScanHistory]);

  const handleClearHistory = useCallback(() => {
    Alert.alert(
      'Clear Scan History',
      'Are you sure you want to clear all scan history? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearScanHistory(MOCK_USER_ID);
              setScanHistory([]);
              setStatistics({
                totalScans: 0,
                uniqueProducts: 0,
                todayScans: 0,
                weekScans: 0,
              });
            } catch (error) {
              console.error('Error clearing scan history:', error);
              Alert.alert('Error', 'Failed to clear scan history');
            }
          },
        },
      ]
    );
  }, []);

  const handleScanItemPress = useCallback(async (scan: ScanHistory) => {
    if (scan.productId) {
      // Navigate to product details
      navigation.navigate('product-details' as never, {
        productId: scan.productId,
        productName: 'Product Details',
      } as never);
    } else {
      // Try to find product by barcode
      const product = await findProductByBarcode(scan.barcode);
      if (product) {
        navigation.navigate('product-details' as never, {
          productId: product.id,
          productName: product.name,
        } as never);
      } else {
        Alert.alert('Product Not Found', 'This product is no longer available in our catalog.');
      }
    }
  }, [navigation]);

  const renderHeader = useCallback(() => (
    <LinearGradient
      colors={[Colors.primary.start, Colors.primary.end]}
      style={styles.header}
    >
      <TouchableOpacity 
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <FontAwesome name="arrow-left" size={20} color="#FFFFFF" />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Scan History</Text>
      <TouchableOpacity 
        style={styles.clearButton}
        onPress={handleClearHistory}
      >
        <FontAwesome name="trash" size={20} color="#FFFFFF" />
      </TouchableOpacity>
    </LinearGradient>
  ), [navigation, handleClearHistory]);

  const renderStatistics = useCallback(() => (
    <ShadowCard style={styles.statsCard}>
      <Text style={styles.statsTitle}>Scan Statistics</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{statistics.totalScans}</Text>
          <Text style={styles.statLabel}>Total Scans</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{statistics.uniqueProducts}</Text>
          <Text style={styles.statLabel}>Unique Products</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{statistics.todayScans}</Text>
          <Text style={styles.statLabel}>Today</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{statistics.weekScans}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </View>
      </View>
    </ShadowCard>
  ), [statistics]);

  const renderScanItem = useCallback(({ item }: { item: ScanHistory }) => {
    const scanDate = new Date(item.timestamp);
    const isToday = scanDate.toDateString() === new Date().toDateString();
    
    return (
      <TouchableOpacity
        style={styles.scanItem}
        onPress={() => handleScanItemPress(item)}
        activeOpacity={0.7}
      >
        <ShadowCard style={styles.scanCard}>
          <View style={styles.scanContent}>
            <View style={styles.scanIcon}>
              <FontAwesome name="qrcode" size={24} color={Colors.primary.start} />
            </View>
            <View style={styles.scanDetails}>
              <Text style={styles.barcode}>{item.barcode}</Text>
              <Text style={styles.scanTime}>
                {isToday ? 'Today' : scanDate.toLocaleDateString()} at{' '}
                {scanDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
              {item.productId && (
                <View style={styles.productFound}>
                  <FontAwesome name="check-circle" size={12} color={Colors.accent.success} />
                  <Text style={styles.productFoundText}>Product Found</Text>
                </View>
              )}
            </View>
            <FontAwesome name="chevron-right" size={16} color={Colors.subtleText} />
          </View>
        </ShadowCard>
      </TouchableOpacity>
    );
  }, [handleScanItemPress]);

  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyContainer}>
      <FontAwesome name="qrcode" size={64} color={Colors.subtleText} />
      <Text style={styles.emptyTitle}>No Scan History</Text>
      <Text style={styles.emptyText}>
        Start scanning barcodes to see your scan history here
      </Text>
    </View>
  ), []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.start} />
      {renderHeader()}
      
      <FlatList
        data={scanHistory}
        keyExtractor={(item) => item.id}
        renderItem={renderScanItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary.start]}
            tintColor={Colors.primary.start}
          />
        }
        ListHeaderComponent={scanHistory.length > 0 ? renderStatistics : null}
        ListEmptyComponent={!loading ? renderEmptyState : null}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  clearButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 20,
    flexGrow: 1,
  },
  statsCard: {
    padding: 20,
    marginBottom: 20,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary.start,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.subtleText,
    textAlign: 'center',
  },
  scanItem: {
    marginBottom: 12,
  },
  scanCard: {
    padding: 16,
  },
  scanContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scanIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  scanDetails: {
    flex: 1,
  },
  barcode: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  scanTime: {
    fontSize: 14,
    color: Colors.subtleText,
    marginBottom: 4,
  },
  productFound: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productFoundText: {
    fontSize: 12,
    color: Colors.accent.success,
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 20,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.subtleText,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 40,
  },
});
