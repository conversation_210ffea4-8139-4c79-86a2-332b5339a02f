import ShadowCard from '@/components/ui/ShadowCard';
import Colors from '@/constants/Colors';
import { FontAwesome } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useNavigation } from 'expo-router';
import React from 'react';
import { FlatList, StatusBar, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

type Product = {
  id: string;
  name: string;
  price: string;
  unit: string;
};

export default function CategoryProductsScreen() {
  const { categoryId, categoryName } = useLocalSearchParams<{ categoryId: string; categoryName: string }>();
  const navigation = useNavigation();

  // Using empty array instead of mock data
  const products: Product[] = [];

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary.start} />
      
      <LinearGradient
        colors={[Colors.primary.start, Colors.primary.end]}
        style={styles.header}
      >
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesome name="arrow-left" size={20} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{categoryName || 'Category'}</Text>
      </LinearGradient>
      
      <View style={styles.content}>
        <ShadowCard style={styles.card}>
          <Text style={styles.cardTitle}>Category ID: {categoryId || 'Unknown'}</Text>
          <Text style={styles.cardText}>This is a placeholder for category products screen.</Text>
        </ShadowCard>
        
        {products.length > 0 ? (
          <FlatList
            data={products}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={styles.productItem}>
                <Text>{item.name}</Text>
              </View>
            )}
          />
        ) : (
          <View style={styles.emptyState}>
            <FontAwesome name="shopping-basket" size={50} color={Colors.border} />
            <Text style={styles.emptyText}>No products available in this category</Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  card: {
    padding: 20,
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: Colors.text,
  },
  cardText: {
    fontSize: 16,
    color: Colors.subtleText,
    lineHeight: 24,
  },
  productItem: {
    padding: 16,
    marginBottom: 8,
    backgroundColor: Colors.card,
    borderRadius: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.subtleText,
    marginTop: 16,
  },
}); 