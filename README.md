# Grocery Delivery App Clone

A modern, gradient-heavy grocery delivery app built with Expo and React Native. This app provides a Zepto-like experience with fast 10-minute delivery promises.

## Features

- **Stunning UI Design**: Beautiful gradient-heavy UI with modern aesthetics
- **Tab-based Navigation**: Easy navigation with bottom tabs
- **Home Screen**: Location picker, category grid, and special offers
- **Search Functionality**: Search for groceries and essentials
- **Cart Management**: Add, view, and checkout items
- **Order Tracking**: View past and current orders
- **User Profile**: Manage profile settings and preferences

## Tech Stack

- Expo SDK 51+
- TypeScript for type safety
- React Navigation for navigation
- Expo Linear Gradient for gradient effects
- React Native Vector Icons for icons
- React Native Safe Area Context for safe area handling

## Getting Started

### Prerequisites

- Node.js (v14 or newer)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (optional)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd making_clone
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npx expo start
```

4. Run on a device or emulator:
- Press `a` to run on Android
- Press `i` to run on iOS
- Scan the QR code with Expo Go app (Android) or Camera app (iOS)

## Project Structure

```
making_clone/
├── app/                    # Main application screens
│   ├── (tabs)/             # Tab-based screens
│   └── _layout.tsx         # Root layout configuration
├── assets/                 # Static assets like images and fonts
├── components/             # Reusable UI components
├── constants/              # Constants like colors and layouts
├── hooks/                  # Custom React hooks
└── package.json            # Project dependencies
```

## Implementation Details

The app is built using Expo Router for navigation, which provides a file-system based routing approach. The main screens are:

- **Home**: Shows location picker, search bar, categories grid, and special offers
- **Search**: Enables searching for products
- **Cart**: Shows items in cart with quantity controls and checkout
- **Orders**: Displays order history with status indicators
- **Profile**: User profile management and settings

## License

MIT
