// Comprehensive test suite for price drop alerts and barcode scanner features

import { 
  findProductByBarcode, 
  addToScanHistory, 
  getScanHistory, 
  getScanStatistics,
  clearScanHistory 
} from '../services/barcodeService';

import { 
  addToWatchlist, 
  removeFromWatchlist, 
  isProductWatched,
  trackDailyPrices,
  detectPriceDrops 
} from '../services/priceTracker';

import { 
  generateRecommendations,
  updateUserPreferences,
  getProductBasedRecommendations,
  getTrendingProducts 
} from '../services/recommendationEngine';

import { 
  sendPriceDropNotification,
  sendBulkPriceDropNotifications,
  sendScanMilestoneNotification 
} from '../services/notifications';

// Mock user and product data
const MOCK_USER_ID = 'test_user_123';
const MOCK_PRODUCT_ID = 'p1';
const MOCK_BARCODE = '1234567890123';

describe('Barcode Scanner Features', () => {
  beforeEach(async () => {
    // Clear scan history before each test
    await clearScanHistory(MOCK_USER_ID);
  });

  test('should find product by barcode', async () => {
    const product = await findProductByBarcode(MOCK_BARCODE);
    
    expect(product).toBeTruthy();
    expect(product?.barcode).toBe(MOCK_BARCODE);
    expect(product?.name).toBe('Organic Bananas');
  });

  test('should return null for invalid barcode', async () => {
    const product = await findProductByBarcode('invalid_barcode');
    expect(product).toBeNull();
  });

  test('should add scan to history', async () => {
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE);
    
    const history = await getScanHistory(MOCK_USER_ID);
    expect(history).toHaveLength(1);
    expect(history[0].barcode).toBe(MOCK_BARCODE);
    expect(history[0].userId).toBe(MOCK_USER_ID);
  });

  test('should get scan statistics', async () => {
    // Add multiple scans
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE);
    await addToScanHistory(MOCK_USER_ID, '2345678901234');
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE); // Duplicate

    const stats = await getScanStatistics(MOCK_USER_ID);
    
    expect(stats.totalScans).toBe(3);
    expect(stats.uniqueProducts).toBe(2);
    expect(stats.todayScans).toBe(3);
  });

  test('should limit scan history to 100 items per user', async () => {
    // Add 105 scans
    for (let i = 0; i < 105; i++) {
      await addToScanHistory(MOCK_USER_ID, `barcode_${i}`);
    }

    const history = await getScanHistory(MOCK_USER_ID);
    expect(history.length).toBeLessThanOrEqual(100);
  });
});

describe('Price Drop Alerts Features', () => {
  beforeEach(async () => {
    // Clear watchlist before each test
    await removeFromWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID);
  });

  test('should add product to watchlist', async () => {
    await addToWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID, {
      notifyOnAnyDrop: true,
      dropThreshold: 5,
    });

    const isWatched = await isProductWatched(MOCK_USER_ID, MOCK_PRODUCT_ID);
    expect(isWatched).toBe(true);
  });

  test('should remove product from watchlist', async () => {
    // First add to watchlist
    await addToWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID, {
      notifyOnAnyDrop: true,
      dropThreshold: 5,
    });

    // Then remove
    await removeFromWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID);

    const isWatched = await isProductWatched(MOCK_USER_ID, MOCK_PRODUCT_ID);
    expect(isWatched).toBe(false);
  });

  test('should track daily prices', async () => {
    // This would typically test the cron job functionality
    // For now, we'll test the function directly
    await expect(trackDailyPrices()).resolves.not.toThrow();
  });

  test('should detect price drops', async () => {
    // Add product to watchlist first
    await addToWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID, {
      notifyOnAnyDrop: true,
      dropThreshold: 5,
    });

    // Test price drop detection
    await expect(detectPriceDrops()).resolves.not.toThrow();
  });
});

describe('Recommendation Engine Features', () => {
  test('should generate personalized recommendations', async () => {
    // Add some scan history to base recommendations on
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE);
    await addToScanHistory(MOCK_USER_ID, '2345678901234');

    const recommendations = await generateRecommendations(MOCK_USER_ID, 5);
    
    expect(Array.isArray(recommendations)).toBe(true);
    expect(recommendations.length).toBeLessThanOrEqual(5);
    
    if (recommendations.length > 0) {
      expect(recommendations[0]).toHaveProperty('product');
      expect(recommendations[0]).toHaveProperty('score');
      expect(recommendations[0]).toHaveProperty('reasons');
      expect(recommendations[0]).toHaveProperty('confidence');
      expect(recommendations[0]).toHaveProperty('type');
    }
  });

  test('should get product-based recommendations', async () => {
    const recommendations = await getProductBasedRecommendations(
      MOCK_PRODUCT_ID, 
      MOCK_USER_ID, 
      3
    );
    
    expect(Array.isArray(recommendations)).toBe(true);
    expect(recommendations.length).toBeLessThanOrEqual(3);
  });

  test('should get trending products', async () => {
    const trending = await getTrendingProducts(MOCK_USER_ID, 5);
    
    expect(Array.isArray(trending)).toBe(true);
    expect(trending.length).toBeLessThanOrEqual(5);
    
    if (trending.length > 0) {
      expect(trending[0].type).toBe('trending');
    }
  });

  test('should update user preferences based on scan history', async () => {
    // Add scan history with different categories
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE); // fruits
    await addToScanHistory(MOCK_USER_ID, '2345678901234'); // dairy
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE); // fruits again

    await expect(updateUserPreferences(MOCK_USER_ID)).resolves.not.toThrow();
  });
});

describe('Notification Features', () => {
  test('should send price drop notification', async () => {
    await expect(
      sendPriceDropNotification(
        MOCK_USER_ID,
        'mock_push_token',
        'Test Product',
        MOCK_PRODUCT_ID,
        100,
        80,
        20
      )
    ).resolves.not.toThrow();
  });

  test('should send bulk price drop notifications', async () => {
    const notifications = [
      {
        userId: MOCK_USER_ID,
        pushToken: 'mock_token_1',
        productName: 'Product 1',
        productId: 'p1',
        oldPrice: 100,
        newPrice: 80,
        dropPercent: 20,
      },
      {
        userId: 'user_2',
        pushToken: 'mock_token_2',
        productName: 'Product 2',
        productId: 'p2',
        oldPrice: 50,
        newPrice: 40,
        dropPercent: 20,
      },
    ];

    await expect(
      sendBulkPriceDropNotifications(notifications)
    ).resolves.not.toThrow();
  });

  test('should send scan milestone notification', async () => {
    await expect(
      sendScanMilestoneNotification(
        MOCK_USER_ID,
        'mock_push_token',
        100,
        '10% discount coupon'
      )
    ).resolves.not.toThrow();
  });
});

describe('Integration Tests', () => {
  test('complete scan-to-recommendation flow', async () => {
    // 1. Scan a product
    await addToScanHistory(MOCK_USER_ID, MOCK_BARCODE);
    
    // 2. Verify scan was recorded
    const history = await getScanHistory(MOCK_USER_ID);
    expect(history.length).toBeGreaterThan(0);
    
    // 3. Update user preferences based on scan
    await updateUserPreferences(MOCK_USER_ID);
    
    // 4. Generate recommendations
    const recommendations = await generateRecommendations(MOCK_USER_ID, 3);
    expect(Array.isArray(recommendations)).toBe(true);
  });

  test('complete price alert flow', async () => {
    // 1. Add product to watchlist
    await addToWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID, {
      notifyOnAnyDrop: true,
      dropThreshold: 10,
    });
    
    // 2. Verify product is being watched
    const isWatched = await isProductWatched(MOCK_USER_ID, MOCK_PRODUCT_ID);
    expect(isWatched).toBe(true);
    
    // 3. Simulate price tracking
    await trackDailyPrices();
    
    // 4. Simulate price drop detection
    await detectPriceDrops();
  });

  test('scan milestone and notification flow', async () => {
    // Add multiple scans to reach milestone
    for (let i = 0; i < 10; i++) {
      await addToScanHistory(MOCK_USER_ID, `barcode_${i}`);
    }
    
    // Check statistics
    const stats = await getScanStatistics(MOCK_USER_ID);
    expect(stats.totalScans).toBe(10);
    
    // Send milestone notification
    await sendScanMilestoneNotification(
      MOCK_USER_ID,
      'mock_push_token',
      stats.totalScans,
      'Congratulations!'
    );
  });
});

describe('Error Handling', () => {
  test('should handle invalid user ID gracefully', async () => {
    const invalidUserId = '';
    
    await expect(getScanHistory(invalidUserId)).resolves.toEqual([]);
    await expect(generateRecommendations(invalidUserId)).resolves.toEqual([]);
  });

  test('should handle invalid product ID gracefully', async () => {
    const invalidProductId = 'invalid_product';
    
    await expect(
      addToWatchlist(MOCK_USER_ID, invalidProductId, {
        notifyOnAnyDrop: true,
        dropThreshold: 5,
      })
    ).resolves.not.toThrow();
  });

  test('should handle network errors gracefully', async () => {
    // These tests would mock network failures
    // For now, we ensure functions don't throw
    await expect(trackDailyPrices()).resolves.not.toThrow();
    await expect(detectPriceDrops()).resolves.not.toThrow();
  });
});

describe('Performance Tests', () => {
  test('should handle large scan history efficiently', async () => {
    const startTime = Date.now();
    
    // Add 1000 scans
    for (let i = 0; i < 1000; i++) {
      await addToScanHistory(MOCK_USER_ID, `barcode_${i % 100}`);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete within reasonable time (adjust threshold as needed)
    expect(duration).toBeLessThan(10000); // 10 seconds
    
    // Verify data integrity
    const stats = await getScanStatistics(MOCK_USER_ID);
    expect(stats.totalScans).toBeLessThanOrEqual(100); // Due to limit
    expect(stats.uniqueProducts).toBeLessThanOrEqual(100);
  });

  test('should generate recommendations efficiently', async () => {
    // Add some scan history
    for (let i = 0; i < 50; i++) {
      await addToScanHistory(MOCK_USER_ID, `barcode_${i % 10}`);
    }
    
    const startTime = Date.now();
    const recommendations = await generateRecommendations(MOCK_USER_ID, 10);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    expect(duration).toBeLessThan(5000); // 5 seconds
    expect(recommendations.length).toBeLessThanOrEqual(10);
  });
});

// Cleanup after all tests
afterAll(async () => {
  await clearScanHistory(MOCK_USER_ID);
  await removeFromWatchlist(MOCK_USER_ID, MOCK_PRODUCT_ID);
});
