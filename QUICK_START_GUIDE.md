# 🚀 Quick Start Guide - Price Drop Alerts & Barcode Scanner

## 📋 Prerequisites

- **Node.js** 18+ installed
- **Expo CLI** installed globally (`npm install -g @expo/cli`)
- **iOS Simulator** or **Android Emulator** set up
- **Physical device** for barcode scanning (recommended)

## ⚡ Quick Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Start Development Server
```bash
npm start
```

### 3. Run on Device/Simulator
```bash
# iOS
npm run ios

# Android
npm run android

# Web (limited barcode functionality)
npm run web
```

## 🎯 Testing the Features

### 📱 Barcode Scanner
1. **Open the app** on a physical device (camera required)
2. **Tap the floating QR code button** on the home screen
3. **Scan any of these test barcodes**:
   - `1234567890123` - <PERSON>
   - `2345678901234` - Fresh Milk 1L
   - `3456789012345` - Whole Wheat Bread
   - `4567890123456` - <PERSON><PERSON><PERSON> 5kg
   - `5678901234567` - Olive Oil 500ml

### 📉 Price Drop Alerts
1. **Navigate to any product** (tap a product or scan a barcode)
2. **Tap the bell icon** in the header to add to watchlist
3. **Customize alert settings** by tapping "Customize Alert Settings"
4. **Set target price** or **drop threshold** preferences
5. **View your watchlist** from the profile tab

### 🛒 Smart Shopping List
1. **Access from profile tab** or create new component
2. **Add items manually** or via barcode scanning
3. **View AI recommendations** by tapping the lightbulb icon
4. **Manage quantities** and mark items as completed

### 📊 Analytics Dashboard
1. **Scan multiple products** to generate data
2. **View scan history** from scanner or profile menu
3. **Check analytics** for insights and trends
4. **Monitor peak hours** and category preferences

## 🔧 Configuration

### Environment Variables
Create `.env` file in root directory:
```env
# Notification Settings
EXPO_PUBLIC_ENABLE_NOTIFICATIONS=true
EXPO_PUBLIC_NOTIFICATION_SOUND=true

# Analytics
EXPO_PUBLIC_ENABLE_ANALYTICS=true
EXPO_PUBLIC_ANALYTICS_ENDPOINT=your_endpoint_here

# Recommendations
EXPO_PUBLIC_RECOMMENDATION_CACHE_TTL=3600
EXPO_PUBLIC_MAX_RECOMMENDATIONS=10
```

### Customizing Mock Data
Edit `services/barcodeService.ts` to add more products:
```typescript
const MOCK_PRODUCTS: Product[] = [
  {
    id: 'your_product_id',
    name: 'Your Product Name',
    barcode: 'your_barcode_here',
    price: 99,
    category: 'your_category',
    // ... other properties
  },
  // Add more products
];
```

## 🎨 Customizing UI

### Colors
Edit `constants/Colors.ts`:
```typescript
export default {
  primary: {
    start: '#your_primary_color',
    end: '#your_secondary_color',
  },
  accent: {
    success: '#your_success_color',
    warning: '#your_warning_color',
    error: '#your_error_color',
  },
  // ... other colors
};
```

### Fonts & Styling
- Modify `StyleSheet` objects in component files
- Update `constants/` files for global styling
- Use existing `ShadowCard` component for consistency

## 🔔 Notification Setup

### iOS Setup
1. **Add to app.json**:
```json
{
  "expo": {
    "notification": {
      "icon": "./assets/notification-icon.png",
      "color": "#ffffff"
    },
    "ios": {
      "supportsTablet": true
    }
  }
}
```

### Android Setup
1. **Add to app.json**:
```json
{
  "expo": {
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      }
    }
  }
}
```

## 🧪 Running Tests

### Unit Tests
```bash
npm test
```

### Specific Test Suites
```bash
# Barcode scanner tests
npm test -- --testNamePattern="Barcode Scanner"

# Price alerts tests
npm test -- --testNamePattern="Price Drop Alerts"

# Recommendation tests
npm test -- --testNamePattern="Recommendation Engine"
```

### Coverage Report
```bash
npm run test:coverage
```

## 🚀 Deployment

### Expo Build
```bash
# Build for iOS
expo build:ios

# Build for Android
expo build:android
```

### EAS Build (Recommended)
```bash
# Install EAS CLI
npm install -g eas-cli

# Configure EAS
eas build:configure

# Build for production
eas build --platform all
```

## 🐛 Troubleshooting

### Common Issues

#### Camera Permissions
- **iOS**: Add camera usage description in `app.json`
- **Android**: Ensure camera permissions in manifest

#### Barcode Not Scanning
- **Check lighting** - ensure good lighting conditions
- **Clean camera lens** - remove any dirt or smudges
- **Try different angles** - scan from various distances

#### Notifications Not Working
- **Check permissions** - ensure notification permissions granted
- **Verify push tokens** - check if push tokens are being generated
- **Test on device** - notifications don't work in simulator

#### Performance Issues
- **Clear cache**: `expo r -c`
- **Restart Metro**: Kill and restart the development server
- **Check memory usage**: Monitor device memory in development

### Debug Mode
Enable debug logging by setting:
```typescript
// In your component
const DEBUG = __DEV__;

if (DEBUG) {
  console.log('Debug info:', data);
}
```

## 📚 Key Files Reference

### Core Components
- `components/BarcodeScanner.tsx` - Camera scanner interface
- `components/PriceHistoryChart.tsx` - Price trend visualization
- `components/PriceAlertSettings.tsx` - Alert customization modal
- `components/SmartShoppingList.tsx` - AI-powered shopping list

### Services
- `services/barcodeService.ts` - Product lookup and scan history
- `services/priceTracker.ts` - Price monitoring and alerts
- `services/recommendationEngine.ts` - AI recommendations
- `services/notifications.ts` - Push notification system
- `services/cronService.ts` - Scheduled background tasks

### Screens
- `app/(tabs)/index.tsx` - Home screen with scanner FAB
- `app/product-details.tsx` - Product details with price alerts
- `app/scan-history.tsx` - Scan analytics and history
- `app/watchlist.tsx` - Price alert management

## 🤝 Contributing

### Code Style
- Use **TypeScript** for all new files
- Follow **React Native** best practices
- Use **functional components** with hooks
- Implement **proper error handling**

### Pull Request Process
1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open Pull Request**

## 📞 Support

### Documentation
- **Full Implementation Guide**: `IMPLEMENTATION_SUMMARY.md`
- **API Documentation**: Check individual service files
- **Component Props**: TypeScript interfaces in component files

### Getting Help
- **GitHub Issues**: Report bugs and feature requests
- **Expo Documentation**: https://docs.expo.dev/
- **React Native Docs**: https://reactnative.dev/docs/

---

## 🎉 You're Ready!

Your React Native shopping app now has:
- ✅ **Barcode scanning** with product lookup
- ✅ **Price drop alerts** with customizable settings
- ✅ **AI recommendations** based on user behavior
- ✅ **Smart shopping lists** with automation
- ✅ **Analytics dashboard** for insights
- ✅ **Comprehensive testing** for reliability

**Happy coding!** 🚀
